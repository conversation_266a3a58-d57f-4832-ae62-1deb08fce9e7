#ifndef ENGINENEXT_H
#define ENGINENEXT_H

#include "nxt_mbuf.h"
#include "nxt_ringbuf.h"
#include "nxt_dissector.h"
#include <yaFtypes/fvalue.h>
#include <stddef.h>
#include <stdint.h>

/* ======================================================
 * typedefs: forward declarations
 * ======================================================
 */
typedef        uint32_t          nxt_ipv4_t;
typedef struct ProtoRecord       precord_t;
typedef struct ProtoSchemaDB     pschema_db_t;
typedef struct nxt_Engine        nxt_engine_t;
typedef struct nxt_Mbuf          nxt_mbuf_t;
typedef struct nxt_Session       nxt_session_t;
typedef struct nxt_ProtoMessage  nxt_pmessage_t;
typedef struct nxt_Dissector     nxt_dissector_t;
typedef struct nxt_ISessionProxy nxt_isessionproxy_t;

/* ======================================================
 * typedefs: callback
 * ======================================================
 */
typedef  void (* nxt_defer_callback_t)(void *userdata);

/* ======================================================
 * enums
 * ======================================================
 */
typedef enum nxt_event
{
    NXT_EVENT_NONE,
    NXT_EVENT_MBUF,
    NXT_EVENT_PACKET_MESSAGE,
    NXT_EVENT_SESSION_MESSAGE,
    NXT_EVENT_APP_LAYER_MESSAGE,
    NXT_EVENT_BEARER_LAYER_MESSAGE,
    NXT_EVENT_UNKNOWN_PACKET,
    NXT_EVENT_PACKET_DROP,
    NXT_EVENT_EXCEPTION_OCCURRED,
    NXT_EVENT_MAX,
} nxt_event_enum;

typedef enum nxt_exception
{
    NXT_EXCEPT_MBUF_ACCESS_OUT_OF_RANGE,
    NXT_EXCEPT_MALFORMED_PACKET,
    NXT_EXCEPT_DISSECTOR_ERROR,
    NXT_EXCEPT_REASSEMBLE_ERROR,
    NXT_EXCEPT_RINGBUF_NO_ENOUGH_DATA,
    NXT_EXCEPT_RINGBUF_NO_SPACE,
} nxt_exception_enum;

typedef enum nxt_direction
{
    NXT_DIR_UNKNOWN = 0,
    NXT_DIR_C2S,
    NXT_DIR_S2C,
} nxt_direction_enum;

typedef enum nxt_stream_read_status
{
    NXT_STREAM_READ_ST_OK           = 0,
    NXT_STREAM_READ_ST_NO_MORE_DATA = 1,
    NXT_STREAM_READ_ST_MISS_HAPPEND = 2,
} nxt_stream_read_status_enum;

typedef enum nxt_dissect_status
{
    NXT_DISSECT_ST_OK               = 0,
    NXT_DISSECT_ST_NONE             = 1,
    NXT_DISSECT_ST_ERROR            = -1,
    NXT_DISSECT_ST_WANT_MORE_BYTES  = -2,
    NXT_DISSECT_ST_VERIFY_FAILED    = -3, // verify 失败，例如 一个使用 imap 端口的 pop 协议，被 imap dissector 检测发现失败;
    NXT_DISSECT_ST_BAD_PACKET       = -4, // 某协议 verify 通过, 但是解析过程中发现它是无效的 packet;
    NXT_DISSECT_ST_UNKNOWN_PROTO    = -5, // 未知;
} nxt_dissect_status_enum;

typedef enum nxt_session_state
{
    NXT_SESSION_ST_NONE = 0,               // 初始状态;
    NXT_SESSION_ST_NOT_RECOGNIZED_YET,     // 暂未进行协议识别
    NXT_SESSION_ST_OK,                     // 会话被识别为一个已知协议，并且目前一切正常;
    NXT_SESSION_ST_UNKNOWN_PROTO,          // 会话被识别为一个未知协议;
    NXT_SESSION_ST_PACKET_MISSED,          // 会话发生了丢包;
    NXT_SESSION_ST_PACKET_MALFORMED,       // 会话中发生了错误, 例如字段长度不正确，checksum 不对，出现了非法消息类型;
    NXT_SESSION_ST_PACKET_NOT_SUPPORT,     // 会话是已知协议，但是其中出现了不支持的 packet, 例如不认识的消息类型;
} nxt_session_state_enum;

// seq 与 len 表示 stream 中 read 到的一个 segment 的信息,
// 如果 status 为 NXT_STATUS_MISS_HAPPEND 则表示发生丢包 segment的相关信息;
typedef struct nxt_StreamReadRes
{
    nxt_stream_read_status_enum status;
    int seq;
    int len;
} nxt_stream_read_res_t;

/* ======================================================
 * data types
 * ======================================================
 */
typedef struct nxt_EngineConfig
{
    const char* linkName;
    const char* trailerName;
} nxt_engine_config_t;

typedef struct nxt_Tuple5Ipv4
{
    uint8_t    proto;
    nxt_ipv4_t srcAddr;  // 一条流中的首个 packet 的 src address; 主机字节序
    nxt_ipv4_t dstAddr;  // 一条流中的首个 packet 的 dst address; 主机字节序
    uint16_t   srcPort;
    uint16_t   dstPort;
} __attribute__((packed)) nxt_tuple_5_ipv4_t;

typedef struct nxt_TcpSegment
{
    nxt_session_t  *session;
    nxt_mbuf_t     *mbuf;
    uint32_t        seq;
    uint16_t        len;
    uint16_t        tcpHeadOffset;
    uint16_t        tcpPayloadOffset;
    uint16_t        trimHead;
    uint16_t        trimTail;
} nxt_tcp_segment_t;

typedef int (*nxt_cb_tcp_event_handler)(nxt_engine_t *engine, nxt_tcp_segment_t *segment, void *userdata);

/* ======================================================
 * interfaces
 * ======================================================
 */
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/*
 * library init and finish
 */
NXT_EXPORT int nxt_init();
NXT_EXPORT int nxt_fini();

/*
 * plugins
 */
NXT_EXPORT int nxt_plugin_dissector_load(const char *pluginPath, char errorBuff[], int buffLen);

/*
 * engine
 */
NXT_EXPORT nxt_engine_t*   nxt_engine_create(nxt_engine_config_t *engineConfig);
NXT_EXPORT int             nxt_engine_destroy(nxt_engine_t *engine);
NXT_EXPORT pschema_db_t*   nxt_engine_get_schemadb(nxt_engine_t *engine);
NXT_EXPORT ya_allocator_t* nxt_engine_get_allocator(nxt_engine_t *engine);
NXT_EXPORT int             nxt_engine_add_event_handler(nxt_engine_t *engine, nxt_event_enum event, nxt_cb_event_handler handler, void *userdata);
NXT_EXPORT int             nxt_engine_run(nxt_engine_t *engine, nxt_mbuf_t *mbuf);

/*
 * handoff
 */
NXT_EXPORT int nxt_handoff_set_key_of_number(nxt_engine_t *engine, uint64_t number);
NXT_EXPORT int nxt_handoff_set_key_of_port_payload(nxt_engine_t *engine, uint16_t serverPort, uint8_t *payload, uint16_t payloadLen);

/*
 * session
 */
NXT_EXPORT nxt_session_t*   nxt_engine_find_session(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5);
NXT_EXPORT nxt_session_t*   nxt_engine_new_tcp_session(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf,
                                                       nxt_dissector_t *tcpDissector, precord_t *precord);
NXT_EXPORT nxt_dissector_t* nxt_session_get_recognized_proto(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT int              nxt_session_on_proto_recognized(nxt_engine_t *engine, nxt_session_t *session, nxt_dissector_t *proto);
NXT_EXPORT int              nxt_session_process_packet(nxt_engine_t *engine, nxt_session_t *session, nxt_direction_enum direction, nxt_mbuf_t *mbuf);
NXT_EXPORT const char*      nxt_session_get_str_repr(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT ya_allocator_t*  nxt_session_get_allocator(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT void*            nxt_session_alloc_userdata(nxt_engine_t *engine, nxt_session_t *session, uint32_t size);
NXT_EXPORT void*            nxt_session_get_userdata(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT precord_t*       nxt_session_create_record(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT void             nxt_session_destroy_record(nxt_engine_t *engine, nxt_session_t *session, precord_t *precord);
NXT_EXPORT int              nxt_session_post_event(nxt_engine_t *engine, nxt_session_t *session, nxt_event_enum event, nxt_mbuf_t *mbuf, precord_t *precord);
/**
 * @brief 伪 session 创建接口
 *    用于创建一个伪 session，用于处理一些不需要实际 session 的情况
 *    通过代理类 nxt_isessionproxy_t 重写接口实现 session 中部分成员自定义行为，具体见 nxt_isessionproxy_t
 *    主要用于单元测试、调试和特殊处理流程
*/
NXT_EXPORT nxt_session_t*   nxt_session_create_psudo_session(nxt_engine_t *engine, nxt_isessionproxy_t *proxy);
NXT_EXPORT void             nxt_session_destory_psudo_session(nxt_engine_t *engine, nxt_session_t *session);


/*
 * resource management
 * packet 与 session lifetime 的内存均不需要手动释放，框架会自动管理
 * nxt_defer_xxx 表示将一个 cleanup 操作延迟到 xxx 生命周期结束的时候调用;
 */
NXT_EXPORT void*           nxt_packet_lifetime_alloc(nxt_engine_t *engine, size_t size);
NXT_EXPORT void            nxt_packet_lifetime_defer(nxt_engine_t *engine, nxt_defer_callback_t cleanup, void *userdata);
NXT_EXPORT void*           nxt_session_lifetime_alloc(nxt_session_t *session, size_t size);
NXT_EXPORT void            nxt_session_lifetime_defer(nxt_session_t *session, nxt_defer_callback_t cleanup, void *userdata);

/*
 * session stream
 */
NXT_EXPORT int            nxt_session_stream_get_avail_len(nxt_engine_t *engine, nxt_session_t *session, nxt_direction_enum direction);
NXT_EXPORT int            nxt_session_stream_read(nxt_engine_t *engine, nxt_session_t *session, nxt_direction_enum direction,
                                       uint8_t *buff, int buffLen, nxt_stream_read_res_t *readStatus);
NXT_EXPORT int            nxt_session_stream_rbread(nxt_engine_t *engine, nxt_session_t *session, nxt_direction_enum direction,
                                         nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus);

/*
 * engine reg-zone
 */
NXT_EXPORT int                nxt_engine_regzone_put_mbuf(nxt_engine_t *engine, nxt_mbuf_t *mbuf);
NXT_EXPORT int                nxt_engine_regzone_put_dissector(nxt_engine_t *engine, nxt_dissector_t *dissector);
NXT_EXPORT nxt_dissector_t*   nxt_engine_regzone_get_dissector(nxt_engine_t *engine);
NXT_EXPORT int                nxt_engine_regzone_put_session(nxt_engine_t *engine, nxt_session_t *session);
NXT_EXPORT int                nxt_engine_regzone_put_direction(nxt_engine_t *engine, nxt_direction_enum  direction);
NXT_EXPORT nxt_direction_enum nxt_engine_regzone_get_direction(nxt_engine_t *engine);

/*
 * engine pkt-zone
 */
NXT_EXPORT int              nxt_engine_pktzone_put_ipv4(nxt_engine_t *engine, uint8_t proto, uint32_t srcIp, uint32_t dstIp);
NXT_EXPORT int              nxt_engine_pktzone_put_ports(nxt_engine_t *engine, uint16_t srcPort, uint16_t dstPort);
NXT_EXPORT int              nxt_engine_pktzone_get_t5_ipv4(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5);
NXT_EXPORT precord_t*       nxt_engine_pktzone_get_precord(nxt_engine_t *engine);  // 每次 engineRun 会自动 put 一个 packet precord

/*
 * engine layer ops
 */

/*
 * 设置当前 layer 的 total_len, 内部会计算出 pktEnding(offset), 表示整个 packet 的有效数据边界(不包括 trailer);
 * 如果不设置当前 layer 的 total_len, 会继承上一层的 pktEnding, 例如: tcp layer 会继承 ip layer 的 pktEnding;
 * 注意区分 header_len 是 layer total_len, ip layer total_len 包括其 header + payload, tcp layer 也是如此;
 * 不是所有的 layer 都能自己知道 layer total_len 但如果知道时应该进行设置，例如 ip 协议; 有助于框架计算出 trailer 长度;
 */
NXT_EXPORT int nxt_engine_layer_set_total_len(nxt_engine_t *engine, int layerLen);

/*
 * 当在eth层中存在trailer长度指示时,设置trailer的长度，
 * 在解析trailer时，对于存在padding的trailer，不需要遍历找到trailer头开始标识，而是通过长度，从包尾向前移动到trailer开始位置
*/
NXT_EXPORT int nxt_engine_layer_set_trailer_len(nxt_engine_t *engine, int trailerLen);

/*
 * engine stats(统计信息)
 */
NXT_EXPORT int nxt_engine_stats_get_total_pkts(nxt_engine_t *engine);
NXT_EXPORT int nxt_engine_stats_get_total_sessions(nxt_engine_t *engine);

/*
 * event and message
 */
NXT_EXPORT const char *   nxt_message_get_text(nxt_pmessage_t *message);
NXT_EXPORT nxt_mbuf_t *   nxt_message_get_mbuf(nxt_pmessage_t *message);
NXT_EXPORT precord_t*     nxt_message_get_precord(nxt_pmessage_t *message);
NXT_EXPORT nxt_session_t* nxt_message_get_session(nxt_pmessage_t *message);
NXT_EXPORT int            nxt_set_tcp_event_handler(nxt_engine_t *engine, nxt_cb_tcp_event_handler callback, void *userdata);

/*
 * exception
 */
NXT_EXPORT void nxt_exception_throw(nxt_exception_enum except, const char *msg);

/*
 * debug
 */
NXT_EXPORT int nxt_debug_foreach_recent_pkts(nxt_engine_t *engine, void (*cb)(nxt_mbuf_t *mbuf, void *userdata), void *userdata);

/*
 * misc
 */
NXT_EXPORT int nxt_engine_on_idle(nxt_engine_t *engine);

#ifdef __cplusplus
}
#endif

#endif /* ENGINENEXT_H */
