#ifndef NXT_ENGINENEXT_H
#define NXT_ENGINENEXT_H

#include "yaEngineNext/nxt_engine.h"
#include "engine_widget.hpp"
#include "mbuf.h"
#include "session.h"
#include "dissector.h"
#include "event_handler.h"
#include "util/timer.h"
#include "util/creator.hpp"
#include "util/noncopyable.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/allocator.hpp>
#include <yaBasicUtils/object_pool.h>
#include <boost/container/pmr/global_resource.hpp>
#include <boost/container/pmr/unsynchronized_pool_resource.hpp>
#include <boost/container/pmr/polymorphic_allocator.hpp>
#include <string.h>
#include <memory>
#include <vector>

namespace pmr = boost::container::pmr;

// engine 是否使用 POOL 类型的 allocator?
#define NXT_ENGINE_USE_POOL_ALLOC

#ifdef NXT_ENGINE_USE_POOL_ALLOC
    #define NXT_ENGINE_ALLOC_TYPE YA_ALLOC_POOL
#else
    #define NXT_ENGINE_ALLOC_TYPE YA_ALLOC_DIRECT
#endif

// engine 是否使用 obj pool 创建 precord
/* #define NXT_ENGINE_USE_OBJ_PRECORD_POOL */

struct nxt_Engine : public nxt::noncopyable
{
public:
    nxt_Engine(const char *linkName = "eth", const char *trailerName = "");
    nxt_Engine(const nxt_engine_config_t *config);
    ~nxt_Engine();

public:
    friend class nxt_PacketScope;

public: // run
    int  engineRun(nxt_mbuf_t *mbuf);

public: // event
    int  addEventHandler(nxt_event_enum event, nxt_cb_event_handler handler, void *userdata);
    int  postSessionEvent(nxt_session_t *session, nxt_event_enum event, nxt_mbuf_t *mbuf, precord_t *precord);
    int  fireEvent(nxt_event_enum event, nxt_pmessage_t *message);
    int  addTcpEventHandler(nxt_cb_tcp_event_handler handler, void *userdata);
    int  fireTcpEvent(nxt_tcp_segment_t *segment);

public: // session 管理
    const nxt_session_t* findSession(nxt_tuple_5_ipv4_t *t5);
    const nxt_session_t* newTcpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf, nxt_dissector_t *tcpDissector, precord_t *precord);
    int                  destroySession(nxt_tuple_5_ipv4_t *t5);
    precord_t*           createSessionRecord(nxt_session_t *session);
    nxt_SessionKeeper*   getIpv4TcpSessionKeeper();

public: // do dissecting
    int callDissector(nxt_dissector_t *dissector, nxt_session_t *session, nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr);
    int dissectPacket(nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr);
    int dissectTrailer(nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr);

public: // layer
    int setCurrentLayerTotalLen(int layerLen);
    int setTrailerLen(int trailerLen);
public: // 统计信息
    uint64_t getStatsTotalPackets();
    uint64_t getStatsTotalSessions();

public: // zone
    nxt_RegisterZone* getRegisterZone();
    nxt_PacketZone*   getPacketZone();

public: // memory and defer callback
    void *pktLifetimeAlloc(size_t size);
    void  pktLifetimeDefer(nxt_defer_callback_t callback, void *userdata);

public: // handoff
    nxt_Dissector* getNextHandoffDissector(nxt_Dissector *dissector, nxt_Session *session, precord_t *precord);
    int            setHandoffKey(uint64_t number);
    int            setHandoffkey(uint16_t serverPort, uint8_t *payload, uint16_t payloadLen);

public: // schemadb and precord
    pschema_db_t* getSchemaDb();
    precord_t*    createRecord();
    void          destroyRecord(precord_t *precord);
    nxt_mbuf_t*   adjustMbufForNextLayer(nxt_mbuf_t *mbuf, int advance, int ending);

public: // allocator, 用于为 nxt_mbuf_t, nxt_Session 等分配内存;
    ya_allocator_t* getPoolAllocator()
    {
        return alloc_.get();
    }

    pmr::memory_resource*    getUnsyncPoolResource();

public: // etc
    nxt_timer_scheduler_t* getTimerScheduler();
    void                   onIdle();
    int                    debugForeachRecentPkts(void (*cb)(nxt_mbuf_t *mbuf, void *userdata), void *userdata);

private:
    void initPrecordObjectPool(uint32_t poolSize);
    void initPrecordLayerPool(player_creator_t *creator, uint32_t poolSize);
    int  registerBaseSchema();
    int  putBaseFields(precord_t *precord, nxt_PacketZone *pktZonePtr);
    int  loadDissectors();
    int  initDissectors();
    int  handleFailedDissection(nxt_mbuf_t *mbuf, nxt_dissect_status_enum status);

private: // internal event
    void onPktDissectBegin(nxt_Mbuf *mbuf);
    void onPktDissectEnd();

private: // 配置信息
    using pschemadb_ptr = std::unique_ptr<pschema_db_t, nxt_PschemadbDeleter>;
    pschemadb_ptr    pschemaDb_;
    std::string      linkName_;
    std::string      trailerName_;
    nxt_dissector_t *linkDissector_    = NULL;
    nxt_dissector_t *trailerDissector_ = NULL;

private: // 内存管理与对象创建
    pmr::unsynchronized_pool_resource                           unsyncPoolResource_;
    yv::allocator<NXT_ENGINE_ALLOC_TYPE>                        alloc_;
    std::unique_ptr<player_creator_t, nxt_PlayerCreatorDeleter> layerCreator_;
    std::unique_ptr<ya_object_pool_t, nxt_PrecordPoolDeleter>   precordObjPool_;
    nxt_ResourceLifetime                                        *pktLifetimePtr_ = NULL; // 指向一个栈上对象; TODO: 考虑将其与 nxt_PacketZone 进行合并；

private: // 定时器
    std::unique_ptr<nxt_timer_scheduler_t, nxt_TimerSchedulerDeleter> timerScheduler_;

private: // session 管理
    std::unique_ptr<nxt_SessionKeeper>          ipv4SessionKeeper_;

private: // packet, layer
    nxt_RegisterZone        registerZone_;        // register 系列变量存储当前 packet 中解析出的相关信息, 用于在各级 dissector 之间进行传递;
    nxt_PacketZone          pktZone_;
    nxt_handoff_key_t       handoffKey_;
    nxt_EngineStats         stats_;
    std::string             strLayers_;

private: // event 管理
    void                                            *userdataTcp_ = NULL;
    nxt_cb_tcp_event_handler                         callbackTcp_ = NULL;
    std::unique_ptr<nxt_EventDispatcherKeeper>       eventDispatchKeeper_;

public:
    using RecentMbuf_ptr     = yv::AllocTrait<nxt_Mbuf>::unique_ptr;

private: // debug
    nxt_RecentItemList<RecentMbuf_ptr, 500> recentPkts_;

// TODO: 将来可能每个 engine 中有不同的 config, dissector, handofftable 等
};

// 说明: 以下类成员函数分开来定义是为了让类的接口更清晰，
// 但对于简单函数我们也希望可以 inline 优化, 所以全部声明为 inline
inline const nxt_session_t* nxt_Engine::findSession(nxt_tuple_5_ipv4_t *t5)
{
    return ipv4SessionKeeper_->findSession(t5);
}

inline uint64_t nxt_Engine::getStatsTotalPackets()
{
    return stats_.packetCnt_;
}

inline uint64_t nxt_Engine::getStatsTotalSessions()
{
    return ipv4SessionKeeper_->getSessionCount();
}

inline nxt_RegisterZone* nxt_Engine::getRegisterZone()
{
    return &registerZone_;
}

inline nxt_PacketZone* nxt_Engine::getPacketZone()
{
    return &pktZone_;
}

inline const nxt_session_t* nxt_Engine::newTcpSession(nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf,
                                                      nxt_dissector_t *tcpDissector, precord_t *precord)
{
    return ipv4SessionKeeper_->newTcpSession(t5, mbuf, tcpDissector, precord);
}

inline int nxt_Engine::destroySession(nxt_tuple_5_ipv4_t *t5)
{
    return ipv4SessionKeeper_->destroySession(t5);
}

inline void* nxt_Engine::pktLifetimeAlloc(size_t size)
{
    return pktLifetimePtr_->alloc(size);
}

inline void  nxt_Engine::pktLifetimeDefer(nxt_defer_callback_t callback, void *userdata)
{
    return pktLifetimePtr_->defer(callback, userdata);
}

inline nxt_SessionKeeper* nxt_Engine::getIpv4TcpSessionKeeper()
{
    return ipv4SessionKeeper_.get();
}

inline nxt_timer_scheduler_t* nxt_Engine::getTimerScheduler()
{
    return timerScheduler_.get();
}

inline pschema_db_t* nxt_Engine::getSchemaDb()
{
    return pschemaDb_.get();
}

inline nxt_Dissector* nxt_Engine::getNextHandoffDissector(nxt_Dissector *dissector, nxt_Session *session, precord_t *precord)
{
    auto findDissector = dissector->findNextDissector(this->handoffKey_, session, precord);
    return findDissector;
}

inline int nxt_Engine::setHandoffKey(uint64_t number)
{
    handoffKey_.type   = NXT_HANDOFF_TYPE_NUMBER;
    handoffKey_.number = number;
    return 0;
}

inline int nxt_Engine::setHandoffkey(uint16_t serverPort, uint8_t *payload, uint16_t payloadLen)
{
    handoffKey_.type                    = NXT_HANDOFF_TYPE_PORT_PAYLOAD;
    handoffKey_.portPayload.serverPort  = serverPort;
    handoffKey_.portPayload.payloadData = payload;
    handoffKey_.portPayload.payloadLen  = payloadLen;
    return 0;
}

inline int nxt_Engine::debugForeachRecentPkts(void (*cb)(nxt_mbuf_t *mbuf, void *userdata), void *userdata)
{
    recentPkts_.foreach([=](const RecentMbuf_ptr &v)
    {
        cb(v.get(), userdata);
    });

    return 0;
}

inline pmr::memory_resource* nxt_Engine::getUnsyncPoolResource()
{
    return &unsyncPoolResource_;
}

inline void nxt_Engine::onPktDissectBegin(nxt_Mbuf *mbuf)
{
    // 更新统计信息
    stats_.packetCnt_++;

    // 记录 recent pkts
    auto p = yv::make_alloc_ptr<nxt_Mbuf>(alloc_.get(), *mbuf); // nxt_Mbuf 拷贝构造
    recentPkts_.push(std::move(p));
}

inline void nxt_Engine::onPktDissectEnd()
{
    registerZone_.reset();
}

#endif /* NXT_ENGINENEXT_H */
