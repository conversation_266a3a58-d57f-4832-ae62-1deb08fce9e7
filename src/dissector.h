#ifndef DISSECTOR_H
#define DISSECTOR_H

#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_dissector.h"
#include "util/noncopyable.h"
#include <yaProtoRecord/precord.h>
#include <unordered_map>
#include <vector>

struct nxt_Dissector : public nxt::noncopyable
{
public:
    friend class nxt_DissectorKeeper;

public:
    nxt_Dissector(nxt_dissector_def_t *def, int index);

    ~nxt_Dissector()
    {
    }

public:
    const char* getProtoName()
    {
        return protoName_;
    }

    uint32_t getProtoId()
    {
        return id_;
    }

    pschema_t* getProtoSchema()
    {
        return this->schema_;
    }

    nxt_dissector_type_enum getDissectorType()
    {
        return this->type_;
    }

    nxt_handoff_type_enum getHandoffType()
    {
        return this->handoff_.type;
    }

    uint32_t getUserdataSize()
    {
        return userdata_.size;
    }

    nxt_session_userdata_t* getUserdata()
    {
        return &userdata_;
    }

public:
    int registerSchemaDef(nxt_engine_t *engine, pschema_db_t *db);
    int doDissect(nxt_engine_t* engine, nxt_session_t* session, nxt_mbuf_t* mbuf);

public: // handoff
    int registerNextDissector(nxt_handoff_key_t key, nxt_Dissector *nextDissector)
    {
        handoff_.registerFun(key, nextDissector, this->handoffUserdata_);
        mountCount_++;
        return 0;
    }

    nxt_Dissector *findNextDissector(nxt_handoff_key_t key, nxt_Session *session, precord_t *precord)
    {
        return handoff_.findFun(key, session, precord, this->handoffUserdata_);
    }

private:
    void onSchemaDefRegisterDone(pschema_t *schema);

private:
    uint32_t                     id_;
    const char                  *protoName_;
    pschema_t                   *schema_;
    nxt_dissector_type_enum      type_;     // dissector 类型，例如 link, trailer 等;
    nxt_cb_dissector_schema_reg  schemaRegFun_;
    nxt_cb_dissector_dissect     dissectFun_;
    nxt_cb_dissector_fini        finiFun_    = NULL;
    nxt_session_userdata_t       userdata_;
    nxt_handoff_t                handoff_;
    nxt_handoff_mnt_t           *mountAt_; // mount_at 指向 fs_dissector_keeper.dissector_registry 管理的 nxt_dissector_def_t 链表，内存来自 global static
    uint16_t                     mountCount_ = 0;
    std::vector<nxt_Dissector*>  handoffTable_;
    void *                       handoffUserdata_;
};

int nxt_dissector_init_all();

int nxt_dissector_finish_all();

nxt_dissector_t* nxt_dissector_get_by_id(int id);

int nxt_dissector_load_dissectors_from_registry();

int nxt_dissector_resolve_handoff();

int nxt_plugin_dissector_unload_all();

#endif /* DISSECTOR_H */

