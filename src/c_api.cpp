#include "yaEngineNext/nxt_engine.h"
#include "yaEngineNext/nxt_util.h"
#include "proto_message.h"
#include "dissector.h"
#include <yaBasicUtils/macro.h>
#include <string.h>
#include <assert.h>
#include <stdarg.h>
#include "engine.h"

int nxt_init()
{
    nxt_dissector_init_all();
    nxt_dissector_load_builtin();
    return 0;
}

int nxt_fini()
{
    nxt_plugin_dissector_unload_all();
    nxt_dissector_finish_all();
    return 0;
}

nxt_engine_t *nxt_engine_create(nxt_engine_config_t *engineConfig)
{
    if (NULL == engineConfig)
    {
        return new nxt_Engine;
    }

    // TODO: 需要检测 config, 例如配置了无效的 link 与 trailer dissector;
    if (engineConfig->trailerName && !nxt_dissector_is_type_of(engineConfig->trailerName, NXT_DISSECTOR_TYPE_TRAILER))
    {
        printf("无效的 trailer 类型: '%s'.\n", engineConfig->trailerName);
        return NULL;
    }

    return new nxt_Engine(engineConfig);
}

int nxt_engine_destroy(nxt_engine_t *engine)
{
    delete engine;
    return 0;
}

pschema_db_t* nxt_engine_get_schemadb(nxt_engine_t *engine)
{
    return engine->getSchemaDb();
}

ya_allocator_t* nxt_engine_get_allocator(nxt_engine_t *engine)
{
    return engine->getPoolAllocator();
}

int nxt_engine_add_event_handler(nxt_engine_t *engine, nxt_event_enum event, nxt_cb_event_handler handler, void *userdata)
{
    return engine->addEventHandler(event, handler, userdata);
}

int nxt_set_tcp_event_handler(nxt_engine_t *engine, nxt_cb_tcp_event_handler callback, void *userdata)
{
    return engine->addTcpEventHandler(callback, userdata);
}

int nxt_engine_run(nxt_engine_t *engine, nxt_mbuf_t *mbuf)
{
    engine->engineRun(mbuf);
    return 0;
}

int nxt_engine_stats_get_total_pkts(nxt_engine_t *engine)
{
    return engine->getStatsTotalPackets();
}

int nxt_engine_stats_get_total_sessions(nxt_engine_t *engine)
{
    return engine->getStatsTotalSessions();
}

nxt_session_t* nxt_engine_find_session(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5)
{
    return const_cast<nxt_session_t*>(engine->findSession(t5));
}

nxt_session_t* nxt_engine_new_tcp_session(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5, nxt_mbuf_t *mbuf, nxt_dissector_t *tcpDissector, precord_t *precord)
{
    // assert(t5->port_A > t5->port_B && "newly session must pass in a C2S t5.");
    return const_cast<nxt_session_t*>(engine->newTcpSession(t5, mbuf, tcpDissector, precord));
}

void* nxt_packet_lifetime_alloc(nxt_engine_t *engine, size_t size)
{
    return engine->pktLifetimeAlloc(size);
}

void* nxt_session_lifetime_alloc(nxt_session_t *session, size_t size)
{
    return session->alloc(size);
}

void nxt_packet_lifetime_defer(nxt_engine_t *engine, nxt_defer_callback_t cleanup, void *userdata)
{
    engine->pktLifetimeDefer(cleanup, userdata);
}

void nxt_session_lifetime_defer(nxt_session_t *session, nxt_defer_callback_t cleanup, void *userdata)
{
    session->defer(cleanup, userdata);
}

/*======================================================================================================
 * nxt_engine_register_xxx
 *=====================================================================================================*/
int nxt_engine_regzone_put_mbuf(nxt_engine_t *engine, nxt_mbuf_t *mbuf)
{
    return engine->getRegisterZone()->storeMbuf(mbuf);
}

int nxt_engine_regzone_put_dissector(nxt_engine_t *engine, nxt_dissector_t *dissector)
{
    return engine->getRegisterZone()->storeDissector(dissector);
}

nxt_dissector_t* nxt_engine_regzone_get_dissector(nxt_engine_t *engine)
{
    return engine->getRegisterZone()->loadDissector(NULL);
}

int nxt_engine_regzone_put_session(nxt_engine_t *engine, nxt_session_t *session)
{
    return engine->getRegisterZone()->storeSession(session);
}

int nxt_engine_regzone_put_direction(nxt_engine_t *engine, nxt_direction_enum direction)
{
    return engine->getRegisterZone()->storeDirection(direction);
}

nxt_direction_enum nxt_engine_regzone_get_direction(nxt_engine_t *engine)
{
    return engine->getRegisterZone()->loadDirection();
}

int nxt_engine_pktzone_put_ipv4(nxt_engine_t *engine, uint8_t proto, uint32_t srcIp, uint32_t dstIp)
{
    return engine->getPacketZone()->storeIpv4(proto, srcIp, dstIp);
}

int nxt_engine_pktzone_put_ports(nxt_engine_t *engine, uint16_t srcPort, uint16_t dstPort)
{
    return engine->getPacketZone()->storePorts(srcPort, dstPort);
}

int nxt_engine_pktzone_get_t5_ipv4(nxt_engine_t *engine, nxt_tuple_5_ipv4_t *t5)
{
    return engine->getPacketZone()->loadT5Ipv4(t5);
}

precord_t* nxt_engine_pktzone_get_precord(nxt_engine_t *engine)
{
    return engine->getPacketZone()->loadPrecord();
}

ya_allocator_t* nxt_session_get_allocator(nxt_engine_t *engine _U_, nxt_session_t *session)
{
    return session->getAllocator().get();
}

void* nxt_session_alloc_userdata(nxt_engine_t *engine _U_, nxt_session_t *session, uint32_t size)
{
    return session->allocUserdata(size);
}

void* nxt_session_realloc_userdata(nxt_engine_t *engine _U_, nxt_session_t *session, uint32_t size)
{
    return session->rellocUserdata(size);
}

void* nxt_session_get_userdata(nxt_engine_t *engine _U_, nxt_session_t *session)
{
    return session->getUserdata();
}

nxt_mbuf_t *   nxt_message_get_mbuf(nxt_pmessage_t *message)
{
    return message->getMbuf();
}

precord_t* nxt_session_pmessage_get_precord(nxt_pmessage_t* pmessage)
{
    return pmessage->getPrecord();
}

precord_t* nxt_session_create_record(nxt_engine_t *engine, nxt_session_t *session)
{
    return engine->createSessionRecord(session);
}

void nxt_session_destroy_record(nxt_engine_t *engine, nxt_session_t *session _U_, precord_t *precord)
{
    if (NULL == engine)
    {
        return session->getEngine()->destroyRecord(precord);
    }

    return engine->destroyRecord(precord);
}

int nxt_session_post_event(nxt_engine_t *engine, nxt_session_t *session, nxt_event_enum event, nxt_mbuf_t *mbuf, precord_t *precord)
{
    return engine->postSessionEvent(session, event, mbuf, precord);
}

nxt_session_t* nxt_session_create_psudo_session(nxt_engine_t *engine, nxt_isessionproxy_t *proxy)
{
    ya_allocator_t *alloc = ya_allocator_get_default();
    // 创建 session, 需要 t5;
    nxt_tuple_5_ipv4_t t5{0, 0, 0, 0, 0};
    return yv::create_object<nxt_PsudoSession>(alloc, engine->getIpv4TcpSessionKeeper(), 0, &t5, proxy);
}

void nxt_session_destory_psudo_session(nxt_engine_t *engine _U_, nxt_session_t *session)
{
    ya_allocator_t *alloc = ya_allocator_get_default();
    yv::destroy_object<nxt_PsudoSession>(alloc, (nxt_PsudoSession *)session);
}

int nxt_session_process_packet(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_direction_enum direction, nxt_mbuf_t *mbuf)
{
    return session->processPacket(engine, direction, mbuf);
}

int nxt_session_on_proto_recognized(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_dissector_t *proto)
{
    session->onProtoRecognized(proto);
    return 0;
}

nxt_dissector_t* nxt_session_get_recognized_proto(nxt_engine_t *engine _U_, nxt_session_t *session)
{
    return session->getRecognizedProto();
}

const char* nxt_session_get_str_repr(nxt_engine_t *engine _U_, nxt_session_t *session)
{
    return session->getSessionRepr();
}

int nxt_session_stream_get_avail_len(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_direction_enum direction)
{
    return session->getAvailBytesLen(direction);
}

int nxt_session_stream_read(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_direction_enum direction,
                            uint8_t *buff, int buffLen, nxt_stream_read_res_t *readStatus)
{
    return session->read(direction, buff, buffLen, readStatus);
}

int nxt_session_stream_rbread(nxt_engine_t *engine _U_, nxt_session_t *session, nxt_direction_enum direction,
                              nxt_ringbuf_t *rbuf, uint32_t readLen, nxt_stream_read_res_t *readStatus)
{
    if (nxt_ringbuf_get_free_size(rbuf) == 0)
    {
        return -1;
    }

    if (nxt_ringbuf_get_free_size(rbuf) < readLen
        || readLen == 0)
    {   // ringbuf 不足，或者 read_len 为 0 表示"尽可能读取，将 rbuf 填满"
        return session->readToRingbuf(direction, rbuf, nxt_ringbuf_get_free_size(rbuf), readStatus);
    }

    return session->readToRingbuf(direction, rbuf, readLen, readStatus);
}

/*======================================================================================================
 * layer
 *=====================================================================================================*/
int nxt_engine_layer_set_total_len(nxt_engine_t *engine, int layerLen)
{
    return engine->setCurrentLayerTotalLen(layerLen);
}

int nxt_engine_layer_set_trailer_len(nxt_engine_t *engine, int trailerLen)
{
    return engine->setTrailerLen(trailerLen);
}

/*======================================================================================================
 * nxt_exception
 *=====================================================================================================*/
void nxt_exception_throw(nxt_exception_enum except, const char *msg)
{
    throw nxt_Exception(except, msg);
}

/*======================================================================================================
 * nxt_message_xxx
 *=====================================================================================================*/
const char *nxt_message_get_text(nxt_pmessage_t *message)
{
    return message->getMessageText();
}

precord_t* nxt_message_get_precord(nxt_pmessage_t *message)
{
    return message->getPrecord();
}

nxt_session_t* nxt_message_get_session(nxt_pmessage_t *message)
{
    return message->getSession();
}

/*======================================================================================================
 * nxt_handoff
 *=====================================================================================================*/
int nxt_handoff_set_key_of_number(nxt_engine_t *engine, uint64_t number)
{
    return engine->setHandoffKey(number);
}

int nxt_handoff_set_key_of_port_payload(nxt_engine_t *engine, uint16_t port, uint8_t *payload, uint16_t payloadLen)
{
    return engine->setHandoffkey(port, payload, payloadLen);
}

/*======================================================================================================
 * nxt_utils_xxx
 *=====================================================================================================*/
static
int show_field_kv(pfield_t *field, const char *kvFormat)
{
    pfield_desc_t *desc  = precord_field_get_fdesc(field);
    ya_fvalue_t   *value = precord_field_get_fvalue(field);
    if (NULL == value)
    {   // blank field
        printf(kvFormat, pfdesc_get_name(desc), "");
        return 0;
    }

    char *valueString = precord_field_fvalue_to_string(field);
    printf(kvFormat, pfdesc_get_name(desc), valueString);
    ya_fvalue_free_string_repr(valueString);

    return 0;
}

int show_field_of_layer(precord_t *precord, player_t *layer)
{
    // 打印第一个非空 field
    pfield_t *field = NULL;
    for (field = precord_field_get_first_from_layer_of(layer);
         field != NULL && precord_field_get_fvalue(field) == NULL;
         field = precord_field_get_next(precord, field))
        ; // blank
    show_field_kv(field, "%s: \"%s\", ");

    if (NULL == field)
    {
        return 0;
    }

    // 打印剩余 field
    for (field = precord_field_get_next(precord, field);
         field != NULL;
         field = precord_field_get_next(precord, field))
    {
        show_field_kv(field, "%s: \"%s\", ");
    }

    return 0;
}

static
int show_field_of_layer_show_blank(precord_t *precord, player_t *layer)
{
    // 打印第一个非空 field
    pfield_t *field = NULL;
    for (field = precord_field_get_first_from_layer_of(layer);
         field != NULL;
         field = precord_field_get_next(precord, field))
    {
        show_field_kv(field, "%s: \"%s\", ");
    }

    return 0;
}

int nxt_util_show_precord(precord_t *precord)
{
    player_t     *layer      = precord_layer_get_top(precord);
    const char   *protoName = precord_layer_get_layer_name(layer);

    printf("{proto: \"%s\", ", protoName);

    for (player_t *l = precord_layer_get_first(precord);
         l != 0;
         l = precord_layer_get_next(precord, l))
    {
        show_field_of_layer_show_blank(precord, l);
    }

    printf("\b\b}\n"); // backspace, 删除多余的 " ,"
    return 0;
}

/*======================================================================================================
 * nxt_debug
 *=====================================================================================================*/
int nxt_debug_foreach_recent_pkts(nxt_engine_t *engine, void (*cb)(nxt_mbuf_t *mbuf, void *userdata), void *userdata)
{
    return engine->debugForeachRecentPkts(cb, userdata);
}

int nxt_engine_on_idle(nxt_engine_t *engine)
{
    engine->onIdle();
    return 0;
}
