#include "engine.h"
#include "proto_message.h"
#include "exception.hpp"
#include "yaEngineNext/nxt_util.h"
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/memory.hpp>

#define NXT_TIMER_PRECISION         100 // 100ms
#define NXT_PRECORD_OBJ_POOL_SIZE   5000
#define NXT_PRECORD_LAYER_POOL_SIZE 2000

int precreate_precord_layers(player_creator_t *creator);

nxt_Engine::nxt_Engine(const char *linkName /* = "eth" */, const char *trailerName /* = "" */)
    : pschemaDb_(pschema_db_create())
    , linkName_(linkName ? linkName : "eth")
    , trailerName_(trailerName ? trailerName : "")
    , timerScheduler_(nxt_timer_scheduler_create(alloc_.get(), NXT_TIMER_PRECISION), nxt_TimerSchedulerDeleter(alloc_.get()))
    , ipv4SessionKeeper_(yv::make_unique<nxt_SessionKeeper>(this, this->alloc_.get(), timerScheduler_.get()))
{
    nxt_dissector_resolve_handoff();

    this->registerBaseSchema();

    this->initDissectors();

    eventDispatchKeeper_ = yv::make_unique<nxt_EventDispatcherKeeper>();

    // 初始化 link 与 trailer dissector;
    linkDissector_    = nxt_dissector_get_by_name(linkName_.c_str());

    if (!trailerName_.empty())
    {
        trailerDissector_ = nxt_dissector_get_by_name(trailerName_.c_str());
    }

    // precord object pool
#ifdef NXT_ENGINE_USE_OBJ_PRECORD_POOL
    this->initPrecordObjectPool(NXT_PRECORD_OBJ_POOL_SIZE);
#endif

    // 创建 layer pool, 并且预创建一批 layers;
    layerCreator_.reset(precord_layer_pool_create(this->getSchemaDb()));
    this->initPrecordLayerPool(layerCreator_.get(), NXT_PRECORD_LAYER_POOL_SIZE);
}

nxt_Engine::nxt_Engine(const nxt_engine_config_t *config)
    : nxt_Engine(config->linkName, config->trailerName)
{
}

nxt_Engine::~nxt_Engine()
{
}

void nxt_Engine::initPrecordObjectPool(uint32_t poolSize)
{
    auto pool =  ya_object_pool_create(poolSize,
                                              (ya_object_pool_reset_obj_fun)precord_reset,
                                              (ya_object_pool_destroy_obj_fun)precord_destroy);
    precordObjPool_.reset(pool);

    for (int i = 0; i<NXT_PRECORD_OBJ_POOL_SIZE; i++)
    {
        precord_t *precord = precord_create_ex(this->getSchemaDb(), PRECORD_FLAG_NONE);
        ya_object_pool_fill_obj(precordObjPool_.get(), precord);
    }
}

void nxt_Engine::initPrecordLayerPool(player_creator_t *creator, uint32_t defaultCnt)
{
    player_precreate_desc_t desc[] = {
        {"eth",  5000},
        {"ipv4", 5000},
        {"tcp",  5000},
        {"udp",  5000},
    };

    precord_layer_pool_precreate(creator, alloc_.get(), defaultCnt, desc, ARRAY_LEN(desc));
}

int nxt_Engine::addEventHandler(nxt_event_enum event, nxt_cb_event_handler handler, void *userdata)
{
    return eventDispatchKeeper_->addEventHandler(event, handler, userdata);
}

int nxt_Engine::addTcpEventHandler(nxt_cb_tcp_event_handler handler, void *userdata)
{
    this->callbackTcp_ = handler;
    this->userdataTcp_ = userdata;
    return 0;
}

inline int nxt_Engine::fireEvent(nxt_event_enum event, nxt_pmessage_t *pmessage)
{
    return eventDispatchKeeper_->fireEvent(event, this, pmessage);
}

int nxt_Engine::fireTcpEvent(nxt_tcp_segment_t *segment)
{
    if (callbackTcp_)
    {
        return callbackTcp_(this, segment, userdataTcp_);
    }

    return 0;
}

precord_t* nxt_Engine::createSessionRecord(nxt_session_t *session _U_)
{   // TODO: 需要 precord 提供高级 api, 提升性能，这里总是有通过 layer 名字找到 layer 的开销;
    // 最好可以直接从 precord clone 得到一个新的 precord, 但是只需要 base layer;
    precord_t *newPrecord       = this->createRecord();
    precord_t *enginePktPrecord = this->pktZone_.getPrecord();
    if (NULL == enginePktPrecord)
    {
        return newPrecord;
    }

    for (pschema_t *s = pschema_get_first_base(this->getSchemaDb());
         s != NULL;
         s = pschema_get_next(this->getSchemaDb(), s))
    {
        const char* layerName = pschema_get_proto_name(s);

        player_t *layerFrom = precord_layer_get_by_name(enginePktPrecord, layerName);
        player_t *layerTo   = precord_layer_get_by_name(newPrecord,   layerName);
        precord_layer_clone_to(layerTo, layerFrom);
    }

    stats_.recordCnt_++;
    return newPrecord;
}

inline precord_t* nxt_Engine::createRecord()
{
#ifdef NXT_ENGINE_USE_OBJ_PRECORD_POOL
    auto p = (precord_t *)ya_object_pool_acquire_obj(this->precordObjPool_.get());
    precord_set_layer_creator(p, this->layerCreator_.get());

    // auto put base layer
    for (pschema_t *s = pschema_get_first_base(this->pschemaDb_.get());
         s != NULL;
         s = pschema_get_next(this->pschemaDb_.get(), s))
    {
        precord_layer_put_new_layer_by_schema(p, s);
    }
#else
    auto p = precord_create_ex(this->pschemaDb_.get(), PRECORD_FLAG_AUTO_PUT_BASE);
#endif // NXT_ENGINE_USE_OBJ_PRECORD_POOL
    return p; // 在 postMessage 中交给 nxt_pmessage_t 进行管理
}

void  nxt_Engine::destroyRecord(precord_t *precord)
{
#ifdef NXT_ENGINE_USE_OBJ_PRECORD_POOL
    ya_object_pool_release_obj(this->precordObjPool_.get(), precord);
#else
    precord_destroy(precord);
#endif // NXT_ENGINE_USE_OBJ_PRECORD_POOL
}

int nxt_Engine::postSessionEvent(nxt_session_t *session, nxt_event event, nxt_mbuf_t *mbuf, precord_t *precord)
{
    if (session)
    {
        session->onEvent(event, mbuf, precord);
    }

    nxt_pmessage_t pmessage(mbuf, precord, session);// pmessage 将管理 precord 内存
    this->fireEvent(event, &pmessage);

    return 0;
}

int nxt_Engine::initDissectors()
{
    uint32_t dissectorCnt = nxt_dissector_get_count();
    for (uint32_t i = 0; i < dissectorCnt; i++)
    {
        nxt_dissector_t *dissector = nxt_dissector_get_by_index(i);
        if (dissector == NULL)
        {
            continue;
        }

        // TODO: init_fun 可能不应该在这里批量进行 init, 应该加载时进行 init, 在那里 init 失败后还可以拒绝该插件
        dissector->registerSchemaDef(this, this->getSchemaDb());
    }
    return 0;
}

inline int nxt_Engine::handleFailedDissection(nxt_mbuf_t *mbuf _U_, nxt_dissect_status_enum status)
{
    switch (status)
    {
    case NXT_DISSECT_ST_BAD_PACKET:
    {
        stats_.badPacketCnt_++;
        break;
    }
    case NXT_DISSECT_ST_VERIFY_FAILED:
    {
        // TODO: 进行协议识别解析
        break;
    }

    default:
        ;
    }

    // 暂时不进行任何补救; 以后需要再这里进行协议识别；
    return NXT_DISSECT_ST_ERROR;
}

void nxt_Engine::onIdle()
{
    nxt_timer_scheduler_update(this->getTimerScheduler(), nxt_utils_current_time_ms());
}

int nxt_Engine::setCurrentLayerTotalLen(int layerLen)
{
    getPacketZone()->setLayerTotalLen(layerLen);
    return getPacketZone()->trySetTrailerPadding();
}

int nxt_Engine::setTrailerLen(int trailerLen)
{
    return getPacketZone()->setTrailerLen(trailerLen);
}


inline int nxt_Engine::callDissector(nxt_dissector_t *dissector, nxt_session_t *session, nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr)
{
    // 调整 mbuf 的 window 起止范围为 nextLayer 的开始与结束;
    // 目的是让 dissector 无法接触到不属于它的数据，同时可以通过绝对偏移访问到自己的数据;
    // 例如 dns 中的 domain name 为 pointer 时需要从 udp payload 进行偏移访问,
    // 这要求给到 dns dissector 时 mbuf_view[0] 就是 udp payload 的第一个字节;

    nxt_mbuf_range_t range = nxt_mbuf_range_set(mbuf, pktZonePtr->getNextLayerBegin(), pktZonePtr->getNextLayerEnd());
    int status = dissector->doDissect(this, session, mbuf);
    nxt_mbuf_range_set(mbuf, range.begin, range.end);

    return status;
}

inline int nxt_Engine::dissectTrailer(nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr)
{
    nxt_mbuf_range_t range = nxt_mbuf_range_set(mbuf, pktZonePtr->getTrailerBegin(), pktZonePtr->getTrailerEnd());
    int status = trailerDissector_->doDissect(this, registerZone_.session_, mbuf);
    nxt_mbuf_range_set(mbuf, range.begin, range.end);
    return status;
}

int nxt_Engine::registerBaseSchema()
{   // base schema 表示在所有的 precord 中总是默认会存在的 layer;
    pschema_t* basic = pschema_register_base(this->getSchemaDb(), "basic", "basic layer", "#NONE"); // 添加在 "#NONE" 之后，是第一个 base layer;

    pschema_register_field(basic, "PacketSN",    YA_FT_UINT64,  "packet serial number");
    pschema_register_field(basic, "SessionSN",   YA_FT_UINT64,  "session serial number");
    pschema_register_field(basic, "RecordSN",    YA_FT_UINT64,  "record serial number");
    pschema_register_field(basic, "CaptureTime", YA_FT_UINT64,  "packet capture time");
    pschema_register_field(basic, "CarriedTime", YA_FT_UINT64,  "time carried within packet");
    pschema_register_field(basic, "DissectTime", YA_FT_UINT64,  "time dissecting the packet");
    pschema_register_field(basic, "PacketLen",   YA_FT_UINT32,  "packet length");
    pschema_register_field(basic, "EffectiveLen",YA_FT_UINT32,  "packet effective length(no padding, no trailer)");
    pschema_register_field(basic, "LinkType",    YA_FT_STRING,  "link layer type");
    pschema_register_field(basic, "ProtoLayers", YA_FT_STRING,  "protocol layers in the packet");
    pschema_register_field(basic, "ProtoFlags",  YA_FT_STRING,  "protocol info flags"); // 指示 mpls, vlan, ipv6 等属性;
    pschema_register_field(basic, "C2S",         YA_FT_STRING,  "packet direction");
    pschema_register_field(basic, "SrcIp",       YA_FT_IPv4,    "src ipv4 address");
    pschema_register_field(basic, "DstIp",       YA_FT_IPv4,    "dst ipv4 address");
    pschema_register_field(basic, "SrcPort",     YA_FT_UINT16,  "src port");
    pschema_register_field(basic, "DstPort",     YA_FT_UINT16,  "dst port");

    return 0;
}

inline int nxt_Engine::putBaseFields(precord_t *precord, nxt_PacketZone *pktZonePtr)
{
    // TODO: 是否有一种 auto cursor, 它创建出来了一个 precord_ref, 这样不会有副作用，这个函数之后的执行并不会也操作 basic
    player_t *currentLayer = precord_layer_move_cursor(precord, "basic");
    if (NULL == currentLayer)
    {
        printf("precord_layer_move_cursor to 'basic' failed.\n");
        return -1;
    }

    pktZonePtr->getProtoLayers(this->strLayers_);

    precord_put(precord, "RecordSN",    uinteger64, stats_.recordCnt_);
    precord_put(precord, "SessionSN",   uinteger64, getStatsTotalSessions());
    precord_put(precord, "PacketSN",    uinteger64, stats_.packetCnt_);
    precord_put(precord, "PacketLen",   uinteger,   pktZonePtr->getPktTotalLen());
    precord_put(precord, "EffectiveLen",uinteger,   pktZonePtr->getPktEffectiveLen());
    precord_put(precord, "CaptureTime", uinteger64, pktZonePtr->getCaptureTime());
    precord_put(precord, "DissectTime", uinteger64, pktZonePtr->getDissectTime());
    precord_put(precord, "ProtoLayers", string,     this->strLayers_.c_str());
    precord_put(precord, "C2S",         string,     pktZonePtr->getDirection() == NXT_DIR_C2S ? "C2S": "S2C");

    return 0;
}

class nxt_PacketScope
{
public:
    nxt_PacketScope(nxt_Engine *engine, nxt_mbuf_t *mbuf, time_t time)
        : engine_(engine)
        , pktLifetime_(engine->getPoolAllocator())
    {
        engine_->pktLifetimePtr_ = &pktLifetime_;

        // call onPktDissectBegin
        engine_->onPktDissectBegin(mbuf);

        // init pktZone
        precord_ = engine_->createRecord();
        engine_->pktZone_.init(mbuf, precord_, time);
    }

    ~nxt_PacketScope()
    {
        // reset pktZone
        engine_->pktZone_.reset();
        engine_->destroyRecord(precord_);

        // call onPktDissectEnd
        engine_->onPktDissectEnd();
    }

private:
    nxt_Engine           *engine_;
    precord_t            *precord_;
    nxt_ResourceLifetime  pktLifetime_;
};

int nxt_Engine::engineRun(nxt_mbuf_t *mbuf)
{
    nxt_PacketScope packetScope(this, mbuf, time(NULL)); // 使用 RAII 执行必要的准备与清理操作;

    int dissectStatus = NXT_DISSECT_ST_NONE;
    try
    {
        dissectStatus = this->dissectPacket(mbuf, &pktZone_);
    }
    catch (nxt_Exception &e)
    {
        // 如果 session 存在，更新 session 状态;
        if (registerZone_.session_)
        {
            registerZone_.session_->setSessionStatus(NXT_SESSION_ST_PACKET_MALFORMED);
        }

        // 报告有异常发生
        nxt_mbuf_range_reset(mbuf);                                   // 将 mbuf 的 range 恢复为 [0, buff_size], dump 整个 packet;
        nxt_pmessage_t message(mbuf, pktZone_.getPrecord(),
                               registerZone_.session_);
        this->fireEvent(NXT_EVENT_EXCEPTION_OCCURRED, &message);

        LOG_INFO("nxt_error: exception [%s] occurred at packet %lu, session:%s",
                 e.what(), this->getStatsTotalPackets(), registerZone_.session_ ? registerZone_.session_->getSessionRepr() : "");
    }

    if (dissectStatus != NXT_DISSECT_ST_OK)
    {   // todo: 报错; dissector 可能没有抛出异常，但是也没有解析成功，dissectStatus 将不是 NXT_DISSECT_ST_OK
    }

    // 调度定时器
    nxt_timer_scheduler_update(this->getTimerScheduler(), nxt_utils_current_time_ms());
    return 0;
}

// TODO:
// 需要根据 session 来优化解析，已经知道了协议的 layer 层级时不再需要进行探测识别;
int nxt_Engine::dissectPacket(nxt_mbuf_t *mbuf, nxt_PacketZone *pktZonePtr)
{
    nxt_dissector_t *dissector     = linkDissector_;
    precord_t       *precord       = pktZonePtr->loadPrecord();
    nxt_mbuf_t      *toNextMbuf    = mbuf;
    nxt_session_t   *toNextSession = NULL;
    int              status        = 0;

    // 解析 pkt 上的所有承载层
    while (dissector && dissector->getDissectorType() != NXT_DISSECTOR_TYPE_APP)
    {
        // load current dissector to register
        this->registerZone_.storeDissector(dissector);

        // TODO: 关于 dissector 接口需要重新设计，考虑是否 consumeLen 作为一个输出参数?
        status = this->callDissector(dissector, toNextSession, toNextMbuf, pktZonePtr);
        if (status < 0)
        {
            // 当前 dissector 解析失败, 执行一次 "错误处理"
            // 例如 一个 imap 的协议(使用了 pop 端口), 可能交给 pop 解析器失败了，需要进行 rescue;
            printf("ERROR: dissect failed on proto:%s, packet SN:%lu\n", dissector->getProtoName(), this->stats_.packetCnt_);
            status = this->handleFailedDissection(toNextMbuf, static_cast<nxt_dissect_status_enum>(status));
        }

        // "错误处理" 之后继续检测
        if (status < 0)
        {
            return status;
        }

        // 当前 dissector 受理了目前的 packet layer, 前进 packet 数据 portion，供下一层 dissector 解析
        pktZonePtr->onDissectorDone(dissector, status, toNextMbuf);
        if (nxt_mbuf_get_length(toNextMbuf) <= 0)
        {   // 如果没有'剩余数据', 停止解析
            break;
        }

        // 有可能上一个 dissector 构造了新的 mbuf(通过 registerStore_mbuf) 给 next dissector
        this->registerZone_.loadMbuf(&toNextMbuf);
        this->registerZone_.loadSession(&toNextSession);

        // handoff to next dissector
        dissector = dissector->findNextDissector(this->handoffKey_, toNextSession, precord);
    }

    // 解析 trailer
    if (trailerDissector_ && pktZone_.getTrailerLen() > 0)
    {
        this->dissectTrailer(mbuf, pktZonePtr);
    }

    // 填充 base layer 相关字段;
    pktZone_.storeDirection(registerZone_.direction_);
    this->putBaseFields(pktZone_.getPrecord(), &pktZone_);

    // 未知协议
    if (NULL == dissector)
    {
        return NXT_DISSECT_ST_UNKNOWN_PROTO;
    }

    // 解析应用层协议
    status = this->callDissector(dissector, toNextSession, toNextMbuf, pktZonePtr);
    if (status < 0)
    {
        return status;
    }

    return NXT_DISSECT_ST_OK;
}
