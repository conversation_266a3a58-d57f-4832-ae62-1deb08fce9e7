#include <cstdio>
#include <unistd.h>
#include <yaBasicUtils/allocator.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaEngineNext/nxt_parser.h>
#include <yaBasicUtils/macro.h>
#include <yaBasicUtils/filesystem.hpp>
#include <yaProtoRecord/precord.h>
#include <gmock/gmock.h>

#include <string>

#define CHAR_ARRAY_LEN(array) (sizeof(array) - 1)
using namespace testing;

int load_plugins()
{
    // 加载插件
    char errorBuff[255] = { 0 };
    std::string pluginDir(yv::getAppDir() + "plugins");
    const char kPluginPrefix[] = "yaNxtDissector";

    yv::forDirEntry(pluginDir.c_str(), [&](const char *filePath, bool)
    {
        const char *fileName = basename(const_cast<char*>(filePath));
        if (strncmp(fileName, kPluginPrefix, sizeof(kPluginPrefix) - 1) != 0)
        {   // not a yaNxtDissector
            return -1;
        }

        int res = nxt_plugin_dissector_load(filePath, errorBuff, sizeof errorBuff);
        if (res < 0)
        {
            printf("load plugin error: %s, %s\n", filePath, errorBuff);
            return -1;
        }

        printf("load plugin done: %s\n", filePath);
        return 0;
    });

    return 0;
}


extern "C"
{
  int nxt_parser_smtp_init(nxt_parser_t *parser);
  int nxt_parser_smtp_parse(nxt_parser_t *parser, const uint8_t *buff, uint32_t len, void *userdata);
}

static
const char *precord_get_str_fvalue(precord_t *precord, const char *fname)
{
    ya_fvalue_t *fvalue = precord_fvalue_get(precord, fname);
    if (NULL == fvalue)
    {
        return NULL;
    }

    return ya_fvalue_get_string(fvalue);
}

class smtp_parser : public testing::Test
{
public:
    void SetUp() override
    {
        nxt_init();
        // 获取当前路径
        // load_plugins();
        engine_ = nxt_engine_create(NULL);
        pschema_db_t* db = nxt_engine_get_schemadb(engine_);
        precord_ = precord_create_ex(db, PRECORD_FLAG_NONE);
        precord_layer_put_new_layer(precord_, "smtp");
    }

    void TearDown() override
    {
        precord_destroy(precord_);
        nxt_engine_destroy(engine_);
    }

protected:
    nxt_engine_t *engine_;
    precord_t *precord_;
};

TEST_F(smtp_parser, smtp_command)
{
    const uint8_t kInputMsg[] = "EHLO smtp.example.com\r\n"
                                "MAIL FROM: <<EMAIL>>\r\n"
                                "RCPT TO: <<EMAIL>>\r\n"
                                "DATA\r\n"
                                "Subject: test\r\n"
                                "\r\n";
    ya_allocator_t *alloc = ya_allocator_get_default();
    nxt_parser_t *parser = nxt_parser_create_wa(alloc, nxt_parser_smtp_init);
    int consumeLen = nxt_parser_smtp_parse(parser, kInputMsg, CHAR_ARRAY_LEN(kInputMsg), (void *)precord_);
    // ASSERT_EQ(NXT_PSTATUS_COMPLETE, nxt_parser_get_status(parser));
    ASSERT_EQ(CHAR_ARRAY_LEN(kInputMsg), consumeLen);

    // EXPECT_STREQ("smtp.example.com", precord_get_str_fvalue(precord_, "ehlo"));

    EXPECT_STREQ("<<EMAIL>>", precord_get_str_fvalue(precord_, "mail_from"));
    EXPECT_STREQ("<<EMAIL>>", precord_get_str_fvalue(precord_, "rcpt_to"));

    EXPECT_STREQ("test", precord_get_str_fvalue(precord_, "subject"));

    nxt_parser_destroy_wa(alloc, parser);

}