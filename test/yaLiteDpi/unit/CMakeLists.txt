cmake_minimum_required(VERSION 3.14)

# project
project(yaLiteDpiUnitTest LANGUAGES C CXX VERSION 0.0.1)

set(CMAKE_C_STANDARD 11)
set(CMAKE_CXX_STANDARD 14)

add_compile_options(-Wall -Wextra -Werror)

# import PkgConfig
set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
find_package(GTest REQUIRED)
find_package(yaEngineNext REQUIRED)
pkg_check_modules(glib2 REQUIRED IMPORTED_TARGET glib-2.0)


#
# yaLiteDpiUnitTest
#
add_executable(yaLiteDpiUnitTest
  main_test.cpp
  parser_smtp_test.cpp
  dissector_smtp_test.cpp
  snmp_asn1_test.cpp
  h323_handoff_test.cpp
)

# 单元测试可能对内部实现进行测试(例如 nxt_Session, http_dissector), 这需要它看到 src 目录
target_include_directories(yaLiteDpiUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/src)
target_include_directories(yaLiteDpiUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/yaLiteDpi/plugins)
target_include_directories(yaLiteDpiUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/yaLiteDpi)

# target_link_directories(yaLiteDpiUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/bin/plugins)
include(../plugins/asn1/protocols.cmake)

# Add ASN.1 include directories for all protocols
foreach(PROTOCOL ${ASN1_PROTOCOLS})
    set(ASN1_INCLUDE_DIR "${CMAKE_BINARY_DIR}/yaLiteDpi/plugins/asn1/generated/${PROTOCOL}")
    if(EXISTS ${ASN1_INCLUDE_DIR})
        target_include_directories(yaLiteDpiUnitTest PUBLIC ${ASN1_INCLUDE_DIR})
        target_link_directories(yaLiteDpiUnitTest PUBLIC ${CMAKE_SOURCE_DIR}/build)
        message(STATUS "Added ASN.1 include directory for ${PROTOCOL}: ${ASN1_INCLUDE_DIR}")
    endif()
endforeach()

target_link_libraries(yaLiteDpiUnitTest
  PUBLIC yaEngineNext
  PUBLIC gtest
  PUBLIC gmock
  PUBLIC PkgConfig::glib2
  PUBLIC yaNxtDissector_smtp
  # PUBLIC yaLiteDpi_lib
  pthread
  asn1_snmp
  asn1_h323
  asn1_h225
  asn1_h235
  asn1_h245
)


#
# 暂不将 unit test 自动加入到 test 中；
# 测试流程是:
# 1.单元测试：make unit
# 2.功能测试：make test
# gtest_discover_tests(yaLiteDpiUnitTest)
#

#
# target: 运行单元测试
# make unit
#
add_custom_target(yaLiteDpiUnit
  $(wrapper) $<TARGET_FILE:yaLiteDpiUnitTest> $(args)
)

