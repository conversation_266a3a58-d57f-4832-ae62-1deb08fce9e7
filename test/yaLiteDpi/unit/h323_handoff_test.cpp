#include <gtest/gtest.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaBasicUtils/macro.h>

// ASN.1 includes for direct H.323 ASN.1 parsing tests
extern "C" {
#include "H323Message.h"
#include "AdmissionRequest.h"
#include "CallType.h"
#include "ber_decoder.h"
}
void printf_record(precord_t *record) {
  for (player_t *layer = precord_layer_get_first(record); layer != NULL; layer = precord_layer_get_next(record, layer)) {
    const char *layer_name = precord_layer_get_layer_name(layer);
    precord_layer_move_cursor(record, layer_name);
    printf("record layer---------%s----------------------------\n", layer_name);
    for (pfield_t *field = precord_field_get_first(record); field != NULL; field = precord_field_get_next(record, field)) {
      pfield_desc_t *fdesc = precord_field_get_fdesc(field);
      ya_fvalue_t   *fvalue = precord_field_get_fvalue(field);
      printf("record field---------:%s\n", pfdesc_get_name(fdesc));

      if (fvalue == NULL) {
        printf("       value---------:NULL\n");

        continue;
      }
      printf("       value---------:%s\n", ya_fvalue_to_string_repr(fvalue, BASE_NONE));
    }
  }
}
class H323HandoffTest : public ::testing::Test {
protected:
    void SetUp() override {
        nxt_engine_config_t config = {.linkName = "eth", .trailerName = nullptr};
        engine = nxt_engine_create(&config);
        ASSERT_NE(engine, nullptr);
    }

    void TearDown() override {
        if (engine) {
            nxt_engine_destroy(engine);
        }
    }

    nxt_engine_t *engine = nullptr;
};

// Test TPKT -> Q931 handoff
TEST_F(H323HandoffTest, TpktToQ931Handoff) {
    // Create a simple TPKT packet with Q.931 payload
    // TPKT header: version(1) + reserved(1) + length(2) = 4 bytes
    // Q.931 payload starts with protocol discriminator 0x08
    uint8_t tpkt_packet[] = {
        0x03, 0x00,        // TPKT version 3, reserved 0
        0x00, 0x10,        // Length: 16 bytes total
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        0x04, 0x03, 0x80, 0x90, 0xa2,  // Bearer capability IE
        0x18, 0x03, 0xa9, 0x83, 0x81   // Channel identification IE
    };
    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), tpkt_packet,  sizeof(tpkt_packet));


    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result =  nxt_engine_run(engine, mbuf);

    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Check if TPKT layer was detected
    printf_record(precord);
    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);
}

// Test Q931 -> H225 handoff
TEST_F(H323HandoffTest, Q931ToH225Handoff) {
    // Create a Q.931 packet with User-User IE containing H.225 data
    uint8_t q931_packet[] = {
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        0x7E, 0x10,        // User-User IE (0x7E), length 16
        0x05,              // Protocol discriminator for H.225
        // H.225 ASN.1 data (simplified)
        0x30, 0x0C, 0x02, 0x01, 0x00, 0x02, 0x01, 0x01,
        0x30, 0x04, 0x02, 0x02, 0x00, 0x01
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), q931_packet,  sizeof(q931_packet));

    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result =  nxt_engine_run(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);
    printf_record(precord);

    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);
}

// Test complete TPKT -> Q931 -> H225 chain
TEST_F(H323HandoffTest, CompleteH323Chain) {
    // Create a complete TPKT packet containing Q.931 with H.225 data
    uint8_t complete_packet[] = {
        // TPKT header
        0x03, 0x00,        // TPKT version 3, reserved 0
        0x00, 0x20,        // Length: 32 bytes total
        
        // Q.931 header
        0x08,              // Q.931 protocol discriminator
        0x01,              // Call reference length
        0x00,              // Call reference value
        0x05,              // Message type: SETUP
        
        // Q.931 IEs
        0x04, 0x03, 0x80, 0x90, 0xa2,  // Bearer capability IE
        0x18, 0x03, 0xa9, 0x83, 0x81,  // Channel identification IE
        
        // User-User IE with H.225 data
        0x7E, 0x10,        // User-User IE (0x7E), length 16
        0x05,              // Protocol discriminator for H.225
        // H.225 ASN.1 data (simplified)
        0x30, 0x0C, 0x02, 0x01, 0x00, 0x02, 0x01, 0x01,
        0x30, 0x04, 0x02, 0x02, 0x00, 0x01
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), complete_packet, sizeof(complete_packet));
    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result = nxt_engine_run(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Check if all three layers were detected by trying to get them
    player_t *tpkt_layer = precord_layer_get_by_name(precord, "tpkt");
    player_t *q931_layer = precord_layer_get_by_name(precord, "q931");
    player_t *h225_layer = precord_layer_get_by_name(precord, "h225");

    EXPECT_NE(tpkt_layer, nullptr);
    EXPECT_NE(q931_layer, nullptr);
    EXPECT_NE(h225_layer, nullptr);

    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);
}

// Test H.323 RAS message parsing using real pcap data
TEST_F(H323HandoffTest, H323RasMessageParsing) {
    // Test using yaLiteDpi to process the actual pcap file
    // This simulates how the engine would process real H.323 traffic

    // Create a simple test to verify H.323 plugin is loaded and working
    // We'll create a minimal valid UDP packet to port 1719
    uint8_t simple_udp_frame[] = {
        // Ethernet header (14 bytes)
        0x00, 0x11, 0x22, 0x33, 0x44, 0x55,  // Destination MAC
        0x66, 0x77, 0x88, 0x99, 0xaa, 0xbb,  // Source MAC
        0x08, 0x00,                          // EtherType: IPv4

        // IPv4 header (20 bytes)
        0x45, 0x00, 0x00, 0x20,              // Version, IHL, DSCP, Total Length (32)
        0x00, 0x01, 0x00, 0x00,              // Identification, Flags, Fragment Offset
        0x40, 0x11, 0x00, 0x00,              // TTL (64), Protocol (UDP), Header Checksum (will be ignored)
        0xc0, 0xa8, 0x01, 0x01,              // Source IP: ***********
        0xc0, 0xa8, 0x01, 0x02,              // Destination IP: ***********

        // UDP header (8 bytes)
        0x12, 0x34,                          // Source Port: 4660
        0x06, 0xb7,                          // Destination Port: 1719 (H.323 RAS)
        0x00, 0x08,                          // Length: 8 (header only)
        0x00, 0x00,                          // Checksum (ignored)
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), simple_udp_frame, sizeof(simple_udp_frame));
    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result = nxt_engine_run(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Print the record for debugging - should show eth, ipv4, udp layers
    printf_record(precord);

    // Check basic layers are present
    player_t *eth_layer = precord_layer_get_by_name(precord, "eth");
    player_t *ipv4_layer = precord_layer_get_by_name(precord, "ipv4");
    player_t *udp_layer = precord_layer_get_by_name(precord, "udp");

    EXPECT_NE(eth_layer, nullptr);
    EXPECT_NE(ipv4_layer, nullptr);
    EXPECT_NE(udp_layer, nullptr);

    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);
}

// Test H.225.0 CS message parsing using real pcap data
TEST_F(H323HandoffTest, H225CSMessageParsing) {
    // TPKT + Q.931 + H.225.0 CS releaseComplete from Frame 39 of h323-sip_sip.pcap
    // This is the TCP payload (after IP and TCP headers)
    uint8_t h225_cs_packet[] = {
        // TPKT header
        0x03, 0x00, 0x00, 0x31,  // Version 3, Reserved 0, Length 49

        // Q.931 header
        0x08,        // Protocol discriminator: Q.931
        0x02,        // Call reference value length: 2
        0x0D, 0x6C,  // Call reference value: 0d6c
        0x5A,        // Message type: RELEASE COMPLETE (0x5a)

        // Cause IE
        0x08, 0x02, 0x80, 0x90,  // Cause IE: Normal call clearing

        // User-user IE
        0x7E, 0x21,  // User-user IE, length 33
        0x05,        // Protocol discriminator: X.208 and X.209 coded user information

        // H.225.0 CS releaseComplete message (ASN.1 encoded)
        0x25, 0x80, 0x06, 0x00, 0x08, 0x91, 0x4A, 0x00, 0x02, 0x31, 0x70, 0x11, 0x00, 0x10, 0x8A, 0x30,
        0x16, 0xFF, 0x29, 0x01, 0x00, 0x10, 0x09, 0x1B, 0x5C, 0x98, 0x7A, 0xA9, 0x85, 0x17, 0x01, 0x00
    };

    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), h225_cs_packet, sizeof(h225_cs_packet));
    ASSERT_NE(mbuf, nullptr);

    // Process the packet through the engine
    int result = nxt_engine_run(engine, mbuf);
    EXPECT_GE(result, 0);

    // Get the protocol record
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    ASSERT_NE(precord, nullptr);

    // Check if TPKT, Q.931, and H.225 layers were detected
    player_t *tpkt_layer = precord_layer_get_by_name(precord, "tpkt");
    player_t *q931_layer = precord_layer_get_by_name(precord, "q931");
    player_t *h225_layer = precord_layer_get_by_name(precord, "h225");

    EXPECT_NE(tpkt_layer, nullptr);
    EXPECT_NE(q931_layer, nullptr);
    EXPECT_NE(h225_layer, nullptr);

    // Print the record for debugging
    printf_record(precord);

    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);
}

// Test direct H.323 ASN.1 parsing (similar to SNMP tests)
TEST_F(H323HandoffTest, H323_ASN1_ParseAdmissionRequest) {
    // H.323 RAS admissionRequest message (ASN.1 BER encoded)
    // This is the actual H.323 payload from the pcap file
    static const uint8_t h323_ras_data[] = {
        0x09, 0x5F, 0x5A, 0x00, 0x00, 0x52, 0x42, 0x38, 0x38, 0x30, 0x46, 0x43, 0x30, 0x30, 0x30, 0x30,
        0x30, 0x30, 0x30, 0x33, 0x30, 0x0A, 0x34, 0x39, 0x39, 0x37, 0x34, 0x35, 0x30, 0x36, 0x32, 0x39,
        0x30, 0x05, 0x48, 0x2D, 0x53, 0x52, 0x56, 0x80, 0x04, 0x55, 0x5A, 0x61, 0x41, 0x82, 0x02, 0xE4,
        0x37, 0x83, 0x02, 0x0D, 0x6C, 0x84, 0x10, 0x8A, 0x30, 0x16, 0xFF, 0x29, 0x01, 0x00, 0x10, 0x09,
        0x1C, 0x5C, 0x98, 0x7A, 0xA9, 0x85, 0x17, 0x85, 0x01, 0x80, 0x86, 0x10, 0x8A, 0x30, 0x16, 0xFF,
        0x29, 0x01, 0x00, 0x10, 0x09, 0x1B, 0x5C, 0x98, 0x7A, 0xA9, 0x85, 0x17, 0x87, 0x06, 0x47, 0x6B,
        0x37, 0x32, 0x30, 0x36, 0x88, 0x01, 0x00
    };

    // Test direct ASN.1 parsing of H.323 RAS message
    H323Message_t *message = nullptr;
    asn_dec_rval_t result = ber_decode(0, &asn_DEF_H323Message, (void **)&message,
                                       h323_ras_data, sizeof(h323_ras_data));

    ASSERT_EQ(result.code, RC_OK);
    ASSERT_GT(result.consumed, 0);  // Should consume some bytes
    ASSERT_LE(result.consumed, sizeof(h323_ras_data));  // Should not consume more than available
    ASSERT_NE(message, nullptr);

    // Check message type - should be admissionRequest
    EXPECT_EQ(message->present, H323Message_PR_admissionRequest);

    // Check some fields of the admission request
    if (message->present == H323Message_PR_admissionRequest) {
        AdmissionRequest_t *admReq = &message->choice.admissionRequest;

        // Check request sequence number (should be 24410 = 0x5F5A)
        EXPECT_EQ(admReq->requestSeqNum, 24410);

        // Check call type (should be pointToPoint)
        EXPECT_EQ(admReq->callType.present, CallType_PR_pointToPoint);

        // Check endpoint identifier exists and has content
        EXPECT_GT(admReq->endpointIdentifier.size, 0);
    }

    ASN_STRUCT_FREE(asn_DEF_H323Message, message);
}
