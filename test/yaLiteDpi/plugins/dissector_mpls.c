#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "mpls"

// 添加枚举
typedef enum {
    MPLS_TYPE_MPLS = 0,
    MPLS_TYPE_ETHERNET = 2,
    MPLS_TYPE_IPv4 = 3,
    MPLS_TYPE_IPv6 = 5,
    MPLS_TYPE_Router_Alert = 4,

} mpls_type_t;

static
int mpls_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    uint32_t value = nxt_mbuf_get_uint32_ntoh(mbuf, 0);
    printf("mpls value = %d\n", value);

    uint32_t label = (value >> 12) & 0xFFFFF;
    uint8_t exp = (value >> 9) & 0x7;
    uint8_t bos = (value >> 8) & 0x1;
    uint8_t ttl = value & 0xFF;

    precord_put(precord, "label", uinteger, label);
    precord_put(precord, "exp",   uinteger, exp);
    precord_put(precord, "bos",   uinteger, bos);
    precord_put(precord, "ttl",   uinteger, ttl);

    // 处理 MPLS 标签
    int offset = 4; // MPLS 标签长度为 4 字节

    // 如果不是栈底标签，则继续处理下一个 MPLS 标签
    if (!bos) {
        nxt_handoff_set_key_of_number(engine, MPLS_TYPE_MPLS); // 继续解析 MPLS
        return offset;
    }

    uint8_t first_nibble = (nxt_mbuf_get_uint8(mbuf, offset) >> 4) & 0x0F;

    switch (first_nibble) {
        case 4: // ipv4
            nxt_handoff_set_key_of_number(engine, MPLS_TYPE_IPv4);
            break;
        case 6: // ipv6
            nxt_handoff_set_key_of_number(engine, MPLS_TYPE_IPv6);
            break;
        case 0: // pw ethernet
            offset += 4;
            nxt_handoff_set_key_of_number(engine, MPLS_TYPE_ETHERNET);
            break;
        default:
            break;
    }

    return offset;
}

static
int mpls_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "mpls");
    pschema_register_field(pschema,    "label",            YA_FT_UINT16,  "label");  // 20bit
    pschema_register_field(pschema,    "exp",              YA_FT_UINT8,   "exp");    // 3bit, Experimenter
    pschema_register_field(pschema,    "bos",              YA_FT_UINT8,   "bos");    // 1bit, Bottom of Label Stack
    pschema_register_field(pschema,    "ttl",              YA_FT_UINT8,   "ttl");    // 8bit, Time To Live
     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "mpls",
    .schemaRegFun = mpls_schema_reg,
    .dissectFun   = mpls_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("vlan", 0x8847),
        NXT_MNT_NUMBER("mpls", MPLS_TYPE_MPLS),
        NXT_MNT_END,
    },
};

nxt_handoff_mnt_t ethMntAt[] = {
    { "mpls", {NXT_HANDOFF_TYPE_NUMBER,  {.number = MPLS_TYPE_ETHERNET}} },
    { NULL, { NXT_HANDOFF_TYPE_NONE, {0} } },
};

nxt_handoff_mnt_t ipv4MntAt[] = {
    { "mpls", {NXT_HANDOFF_TYPE_NUMBER,  {.number = MPLS_TYPE_IPv4}} },
    { NULL, { NXT_HANDOFF_TYPE_NONE, {0} } },
};

NXT_DISSECTOR_INIT(mpls)
{
    nxt_dissector_register(&gDissectorDef);
    // nxt_dissector_append_handoff("eth", &ethMntAt[0]);
    // nxt_dissector_append_handoff("ipv4", &ipv4MntAt[0]);
}
