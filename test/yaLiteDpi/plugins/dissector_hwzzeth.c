#include "yaEngineNext/nxt_engine.h"
#include <string.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <netinet/in.h>

#define PROTO_NAME "hwzzeth"

struct hwzz_flag {
  uint8_t resv1 : 1;
  uint8_t resv2 : 1;
  uint8_t firstFrame : 1;
  uint8_t direction : 1;
  uint8_t resv3 : 1;
  uint8_t resv4 : 1;
  uint8_t fin : 1;
  uint8_t resv5 : 1;
};

struct hwzz_data_source {
  uint8_t fromPosition : 4;
  uint8_t operatorInfo : 4;
};

struct hwzz_eth_hdr {
  uint8_t                 queueNum;
  struct hwzz_data_source dataSource;
  uint32_t                imsiHash;
  struct hwzz_flag        flag;
  uint8_t                 type;
  uint16_t                totLen;
  uint16_t                trailerLen;
} __attribute__((packed));

typedef struct _value_string {
    uint32_t      value;
    const char*   strptr;
} value_string;

static const value_string
kGOperatorTypeHw[] = {
    { 0X01,    "CTCC"        },
    { 0X02,    "中国网通"    },
    { 0X03,    "CUCC"        },
    { 0X04,    "中国长城宽带"},
    { 0X05,    "中国铁通"    },
    { 0X06,    "CMCC"        },
    { 0X08,    "教育部门"    },
    { 0X09,    "中科院"      },
    { 0X0b,    "广电部门"    },
    { 0X63,    "其他"        },
    { 0x00,    NULL          },
};

static const char *value2String(size_t value, const value_string *kvList) {
    if (NULL == kvList) {
      return "";
    }

    while (!(NULL == kvList->strptr && 0 == kvList->value)) {
      if (value == kvList->value) {
        return kvList->strptr;
      }
      kvList++;
    }
    return "";
}

static int ether_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf) {
  precord_t *precord = nxt_engine_pktzone_get_precord(engine);
  precord_layer_put_new_layer_cache(precord, PROTO_NAME);

  struct hwzz_eth_hdr *ethHdr = (struct hwzz_eth_hdr *)nxt_mbuf_get_raw(mbuf, 0);
  uint16_t             totLen = ntohs(ethHdr->totLen);
  uint16_t             trailerLen = ntohs(ethHdr->trailerLen);
  uint16_t             type = 0;
  if (totLen != nxt_mbuf_get_capacity(mbuf)) {
    precord_put(precord, "dst", bytes, nxt_mbuf_get_raw(mbuf, 0), 6);
    precord_put(precord, "src", bytes, nxt_mbuf_get_raw(mbuf, 6), 6);
    goto end;
  }
  nxt_engine_layer_set_trailer_len(engine, trailerLen);
  precord_layer_move_cursor(precord, "4G");

  const char *p = value2String(ethHdr->dataSource.operatorInfo, kGOperatorTypeHw);
  if (p) {
    precord_put(precord, "operator", stringn, p, strlen(p));
  }

end:
  precord_layer_move_cursor(precord, PROTO_NAME);

  type = nxt_mbuf_get_uint16_ntoh(mbuf, 12);
  precord_put(precord, "type", uinteger, type);
  nxt_handoff_set_key_of_number(engine, type);
  return 14;
}

static int ether_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
  /* 注册 schema */
  pschema_t *pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "ethernet");
  pschema_register_field(pschema, "dst", YA_FT_ETHER, "dst mac address");
  pschema_register_field(pschema, "src", YA_FT_ETHER, "src mac address");
  pschema_register_field_ex(pschema, "type", YA_FT_UINT16, "ether type", YA_DISPLAY_BASE_HEX);

  return 0;
}

static nxt_dissector_def_t gDissectorDef = {
    .name = PROTO_NAME,
    .type = NXT_DISSECTOR_TYPE_LINK,
    .schemaRegFun = ether_schema_reg,
    .dissectFun = ether_dissect,
    .handoff = NXT_HANDOFF_DEFAULT,
    .mountAt =
        {
            NXT_MNT_END,
        },
};

NXT_DISSECTOR_INIT(eth) {
  nxt_dissector_register(&gDissectorDef);
  nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "hwzzeth", 0x0800, "ipv4");
}
