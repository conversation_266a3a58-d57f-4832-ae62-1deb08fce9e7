#include "yaEngineNext/nxt_dissector.h"
#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "vlan"

static
int vlan_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // precord_put(precord, "dst",  bytes,    nxt_mbuf_get_raw(mbuf, 0), 6);
    // precord_put(precord, "src",  bytes,    nxt_mbuf_get_raw(mbuf, 6), 6);

    uint16_t type = nxt_mbuf_get_uint16_ntoh(mbuf, 2);
    printf("vlan type = %d\n", type);

    nxt_handoff_set_key_of_number(engine, type);
    return 4;
}

static
int vlan_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "vlan");
    pschema_register_field(pschema,    "flags",            YA_FT_UINT16,  "flags");
     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "vlan",
    .schemaRegFun = vlan_schema_reg,
    .dissectFun   = vlan_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("eth", 0x8100),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(vlan)
{
    nxt_dissector_register(&gDissectorDef);
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "vlan", 0x0800, "ipv4");
}
