-- H235-SECURITY-MESSAGES.asn
--
-- H.235 Security and encryption for H-series (H.323 and other H.245-based) multimedia terminals
-- Based on ITU-T Recommendation H.235
--

-- Module H235-SECURITY-MESSAGES (H.235.0:09/2005)
H235-SECURITY-MESSAGES {itu-t(0) recommendation(0) h(8) h235(235) version(0) 6 security-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- Export all symbols
-- =======================================================================
-- Top level Security Messages
-- =======================================================================
H235SecurityMessage ::= CHOICE {
  authenticationRequest    AuthenticationRequest,
  authenticationConfirm    AuthenticationConfirm,
  authenticationReject     AuthenticationReject,
  keyExchangeRequest       KeyExchangeRequest,
  keyExchangeResponse      KeyExchangeResponse,
  securityCapabilities     SecurityCapabilities,
  encryptionSync           EncryptionSync,
  ...
}

-- =======================================================================
-- Authentication Messages
-- =======================================================================
AuthenticationRequest ::= SEQUENCE {
  authenticationHeader     AuthenticationHeader,
  challenge                ChallengeString OPTIONAL,
  random                   RandomVal OPTIONAL,
  certificate              SEQUENCE OF Certificate OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoToken OPTIONAL,
  integrity                IntegrityMechanism OPTIONAL,
  algorithmOID             OBJECT IDENTIFIER OPTIONAL,
  ...
}

AuthenticationConfirm ::= SEQUENCE {
  authenticationHeader     AuthenticationHeader,
  response                 ChallengeResponse OPTIONAL,
  random                   RandomVal OPTIONAL,
  certificate              SEQUENCE OF Certificate OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoToken OPTIONAL,
  integrity                IntegrityMechanism OPTIONAL,
  algorithmOID             OBJECT IDENTIFIER OPTIONAL,
  ...
}

AuthenticationReject ::= SEQUENCE {
  authenticationHeader     AuthenticationHeader,
  rejectReason             AuthenticationRejectReason,
  ...
}

-- =======================================================================
-- Key Exchange Messages
-- =======================================================================
KeyExchangeRequest ::= SEQUENCE {
  requestId                RequestId,
  dhkey                    DHset OPTIONAL,
  certificate              SEQUENCE OF Certificate OPTIONAL,
  generalID                Identifier OPTIONAL,
  eckasdhkey               ECKASDH OPTIONAL,
  sendersID                Identifier OPTIONAL,
  h235Key                  H235Key OPTIONAL,
  ...
}

KeyExchangeResponse ::= SEQUENCE {
  requestId                RequestId,
  dhkey                    DHset OPTIONAL,
  certificate              SEQUENCE OF Certificate OPTIONAL,
  generalID                Identifier OPTIONAL,
  eckasdhkey               ECKASDH OPTIONAL,
  sendersID                Identifier OPTIONAL,
  h235Key                  H235Key OPTIONAL,
  ...
}

-- =======================================================================
-- Security Capabilities
-- =======================================================================
SecurityCapabilities ::= SEQUENCE {
  nonStandard              SEQUENCE OF NonStandardParameter OPTIONAL,
  encryption               SEQUENCE OF EncryptionCapability OPTIONAL,
  authenticaton            SEQUENCE OF AuthenticationCapability OPTIONAL,
  integrity                SEQUENCE OF IntegrityCapability OPTIONAL,
  keyExchange              SEQUENCE OF KeyExchangeCapability OPTIONAL,
  keyUpdates               SEQUENCE OF KeyUpdateCapability OPTIONAL,
  ...
}

-- =======================================================================
-- Encryption Synchronization
-- =======================================================================
EncryptionSync ::= SEQUENCE {
  nonStandard              NonStandardParameter OPTIONAL,
  synchFlag                INTEGER(0..255),
  h235Key                  H235Key OPTIONAL,
  escrowentry              SEQUENCE OF EscrowData OPTIONAL,
  ...
}

-- =======================================================================
-- Basic Security Types
-- =======================================================================
AuthenticationHeader ::= SEQUENCE {
  nonStandard              NonStandardParameter OPTIONAL,
  authenticationMode       AuthenticationMode,
  timeStamp                TimeStamp OPTIONAL,
  challenge                ChallengeString OPTIONAL,
  random                   RandomVal OPTIONAL,
  certificate              SEQUENCE OF Certificate OPTIONAL,
  integrity                IntegrityMechanism OPTIONAL,
  algorithmOID             OBJECT IDENTIFIER OPTIONAL,
  ...
}

AuthenticationMode ::= CHOICE {
  nonStandard              NonStandardParameter,
  pwdHash                  NULL,
  pwdSymEnc                NULL,
  certSign                 NULL,
  ipsec                    NULL,
  tls                      NULL,
  nonRepudiation           NULL,
  ...
}

ChallengeString ::= OCTET STRING(SIZE(8..128))

ChallengeResponse ::= OCTET STRING(SIZE(8..128))

RandomVal ::= INTEGER

TimeStamp ::= INTEGER(1..4294967295)

RequestId ::= INTEGER(1..4294967295)

Identifier ::= BMPString(SIZE(1..128))

-- =======================================================================
-- Certificate and Token Types
-- =======================================================================
Certificate ::= CHOICE {
  x509v3                   OCTET STRING,
  pgp                      OCTET STRING,
  ...
}

ClearToken ::= SEQUENCE {
  tokenOID                 OBJECT IDENTIFIER,
  timeStamp                TimeStamp OPTIONAL,
  password                 Password OPTIONAL,
  dhkey                    DHset OPTIONAL,
  challenge                ChallengeString OPTIONAL,
  random                   RandomVal OPTIONAL,
  certificate              Certificate OPTIONAL,
  generalID                Identifier OPTIONAL,
  nonStandard              NonStandardParameter OPTIONAL,
  eckasdhkey               ECKASDH OPTIONAL,
  sendersID                Identifier OPTIONAL,
  h235Key                  H235Key OPTIONAL,
  ...
}

CryptoToken ::= CHOICE {
  cryptoEncryptedToken     SEQUENCE {
    tokenOID               OBJECT IDENTIFIER,
    token                  OCTET STRING
  },
  cryptoSignedToken        SEQUENCE {
    tokenOID               OBJECT IDENTIFIER,
    token                  OCTET STRING,
    signature              OCTET STRING,
    signatureOID           OBJECT IDENTIFIER OPTIONAL
  },
  cryptoHashedToken        SEQUENCE {
    tokenOID               OBJECT IDENTIFIER,
    hashedVals             ClearToken,
    token                  OCTET STRING
  },
  cryptoPwdEncr            SEQUENCE {
    tokenOID               OBJECT IDENTIFIER,
    token                  OCTET STRING
  },
  ...
}

Password ::= CHOICE {
  plaintext                BMPString(SIZE(1..128)),
  digested                 SEQUENCE {
    username               BMPString(SIZE(1..128)),
    password               BMPString(SIZE(1..128)),
    digAlg                 AlgorithmIdentifier,
    realm                  BMPString(SIZE(1..128)) OPTIONAL
  },
  ...
}

-- =======================================================================
-- Cryptographic Types
-- =======================================================================
DHset ::= SEQUENCE {
  halfkey                  BIT STRING,
  modSize                  BIT STRING OPTIONAL,
  generator                BIT STRING OPTIONAL,
  ...
}

ECKASDH ::= CHOICE {
  eckasdhp                 SEQUENCE {
    public-key             OCTET STRING,
    modulus                OCTET STRING OPTIONAL,
    base                   OCTET STRING OPTIONAL,
    weierstrassA           OCTET STRING OPTIONAL,
    weierstrassB           OCTET STRING OPTIONAL
  },
  eckasdh2                 SEQUENCE {
    public-key             OCTET STRING,
    fieldSize              OCTET STRING OPTIONAL,
    base                   OCTET STRING OPTIONAL,
    weierstrassA           OCTET STRING OPTIONAL,
    weierstrassB           OCTET STRING OPTIONAL
  },
  ...
}

H235Key ::= CHOICE {
  secureChannel            KeyMaterial,
  sharedSecret             OCTET STRING,
  certProtectedKey         SEQUENCE {
    certID                 Identifier,
    key                    OCTET STRING
  },
  secureSharedSecret       V3KeySyncMaterial,
  ...
}

KeyMaterial ::= BIT STRING

V3KeySyncMaterial ::= SEQUENCE {
  generalID                Identifier OPTIONAL,
  algorithmOID             OBJECT IDENTIFIER OPTIONAL,
  logicalKeylength         INTEGER(0..2147483647) OPTIONAL,
  keyMaterial              BIT STRING OPTIONAL,
  ...
}

-- =======================================================================
-- Capability Types
-- =======================================================================
EncryptionCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  algorithm                OBJECT IDENTIFIER,
  ...
}

AuthenticationCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  authenticationMode       AuthenticationMode,
  ...
}

IntegrityCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  algorithm                OBJECT IDENTIFIER,
  ...
}

KeyExchangeCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  algorithm                OBJECT IDENTIFIER,
  ...
}

KeyUpdateCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  algorithm                OBJECT IDENTIFIER,
  ...
}

-- =======================================================================
-- Integrity and Algorithm Types
-- =======================================================================
IntegrityMechanism ::= CHOICE {
  nonStandard              NonStandardParameter,
  digSig                   NULL,
  iso9797                  SEQUENCE {
    macAlgorithm           OBJECT IDENTIFIER,
    macLength              INTEGER(8..32)
  },
  nonIsoIM                 NonStandardParameter,
  hMAC-MD5                 NULL,
  hMAC-SHA1                NULL,
  ...
}

AlgorithmIdentifier ::= SEQUENCE {
  algorithm                OBJECT IDENTIFIER,
  parameters               ANY DEFINED BY algorithm OPTIONAL
}

-- =======================================================================
-- Error and Reason Types
-- =======================================================================
AuthenticationRejectReason ::= CHOICE {
  neededFeatureNotSupported NULL,
  genericDataReason        NULL,
  invalidCertificate       NULL,
  invalidTime              NULL,
  invalidLoginCredentials  NULL,
  duplicateMediaKey        NULL,
  securityWrongSyncTime    NULL,
  securityReplay           NULL,
  securityWrongGeneralID   NULL,
  securityWrongSendersID   NULL,
  securityIntegrityFailed  NULL,
  securityWrongOID         NULL,
  securityDHmismatch       NULL,
  securityCertificateExpired NULL,
  securityCertificateDateInvalid NULL,
  securityCertificateRevoked NULL,
  securityCertificateNotReadable NULL,
  securityCertificateSignatureInvalid NULL,
  securityCertificateMissing NULL,
  securityCertificateIncomplete NULL,
  securityUnsupportedCertificateAlgOID NULL,
  securityUnknownCA        NULL,
  ...
}

-- =======================================================================
-- Escrow and Additional Types
-- =======================================================================
EscrowData ::= SEQUENCE {
  escrowID                 OBJECT IDENTIFIER,
  escrowValue              BIT STRING
}

NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier    NonStandardIdentifier,
  data                     OCTET STRING
}

NonStandardIdentifier ::= CHOICE {
  object                   OBJECT IDENTIFIER,
  h221NonStandard          SEQUENCE {
    t35CountryCode         INTEGER(0..255),
    t35Extension           INTEGER(0..255),
    manufacturerCode       INTEGER(0..65535)
  }
}

END
