-- H225-MESSAGES.asn
--
-- H.225.0 Call signalling protocols and media stream packetization for packet-based multimedia communication systems
-- Based on ITU-T Recommendation H.225.0
--

-- Module H225-MESSAGES (H.225.0:05/2006)
H225-MESSAGES {itu-t(0) recommendation(0) h(8) h225(225) version(0) 6 h225-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- Export all symbols
-- =======================================================================
-- Top level Messages
-- =======================================================================
H225Message ::= CHOICE {
  setup                Setup-UUIE,
  callProceeding       CallProceeding-UUIE,
  connect              Connect-UUIE,
  alerting             Alerting-UUIE,
  information          Information-UUIE,
  releaseComplete      ReleaseComplete-UUIE,
  facility             Facility-UUIE,
  progress             Progress-UUIE,
  empty                NULL,
  status               Status-UUIE,
  statusInquiry        StatusInquiry-UUIE,
  setupAcknowledge     SetupAcknowledge-UUIE,
  notify               Notify-UUIE,
  ...
}

-- =======================================================================
-- Basic Types
-- =======================================================================
CallIdentifier ::= SEQUENCE {
  guid  OCTET STRING(SIZE(16))
}

ConferenceIdentifier ::= OCTET STRING(SIZE(16))

RequestSeqNum ::= INTEGER(1..65535)

TimeStamp ::= INTEGER(1..**********)

CallReferenceValue ::= INTEGER(0..65535)

-- =======================================================================
-- Address Types
-- =======================================================================
TransportAddress ::= CHOICE {
  ipAddress    SEQUENCE {
    ip    OCTET STRING(SIZE(4)),
    port  INTEGER(0..65535)
  },
  ipSourceRoute SEQUENCE {
    ip       OCTET STRING(SIZE(4)),
    port     INTEGER(0..65535),
    route    SEQUENCE OF OCTET STRING(SIZE(4)),
    routing  CHOICE {
      strict  NULL,
      loose   NULL
    }
  },
  ipxAddress   SEQUENCE {
    node  OCTET STRING(SIZE(6)),
    netnum OCTET STRING(SIZE(4)),
    port  OCTET STRING(SIZE(2))
  },
  ip6Address   SEQUENCE {
    ip    OCTET STRING(SIZE(16)),
    port  INTEGER(0..65535)
  },
  netBios      OCTET STRING(SIZE(16)),
  nsap         OCTET STRING(SIZE(1..20)),
  nonStandardAddress NonStandardParameter,
  ...
}

AliasAddress ::= CHOICE {
  dialedDigits      IA5String(SIZE(1..128)),
  h323-ID           BMPString(SIZE(1..256)),
  url-ID            IA5String(SIZE(1..512)),
  transportID       TransportAddress,
  email-ID          IA5String(SIZE(1..512)),
  partyNumber       PartyNumber,
  mobileUIM         CHOICE {
    ansi-41-uim     OCTET STRING(SIZE(1..14)),
    gsm-uim         SEQUENCE {
      imsi          OCTET STRING(SIZE(3..8)),
      tmsi          OCTET STRING(SIZE(1..4)) OPTIONAL
    }
  },
  ...
}

PartyNumber ::= CHOICE {
  e164Number        PublicPartyNumber,
  dataPartyNumber   NumberDigits,
  telexPartyNumber  NumberDigits,
  privateNumber     PrivatePartyNumber,
  nationalStandardPartyNumber NumberDigits,
  ...
}

PublicPartyNumber ::= SEQUENCE {
  publicTypeOfNumber  PublicTypeOfNumber,
  publicNumberDigits  NumberDigits
}

PrivatePartyNumber ::= SEQUENCE {
  privateTypeOfNumber  PrivateTypeOfNumber,
  privateNumberDigits  NumberDigits
}

NumberDigits ::= IA5String(SIZE(1..128))

PublicTypeOfNumber ::= CHOICE {
  unknown                 NULL,
  internationalNumber     NULL,
  nationalNumber          NULL,
  networkSpecificNumber   NULL,
  subscriberNumber        NULL,
  abbreviatedNumber       NULL,
  ...
}

PrivateTypeOfNumber ::= CHOICE {
  unknown         NULL,
  level2RegionalNumber NULL,
  level1RegionalNumber NULL,
  pISNSpecificNumber   NULL,
  localNumber          NULL,
  abbreviatedNumber    NULL,
  ...
}

-- =======================================================================
-- Non-standard parameter
-- =======================================================================
NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier  NonStandardIdentifier,
  data                   OCTET STRING
}

NonStandardIdentifier ::= CHOICE {
  object           OBJECT IDENTIFIER,
  h221NonStandard  SEQUENCE {
    t35CountryCode    INTEGER(0..255),
    t35Extension      INTEGER(0..255),
    manufacturerCode  INTEGER(0..65535)
  }
}

-- =======================================================================
-- Setup Message
-- =======================================================================
Setup-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  h245Address               TransportAddress OPTIONAL,
  sourceAddress             SEQUENCE OF AliasAddress OPTIONAL,
  sourceInfo                EndpointType,
  destinationAddress        SEQUENCE OF AliasAddress OPTIONAL,
  destCallSignalAddress     TransportAddress OPTIONAL,
  destExtraCallInfo         SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCRV              SEQUENCE OF CallReferenceValue OPTIONAL,
  activeMC                  BOOLEAN,
  conferenceID              ConferenceIdentifier,
  conferenceGoal            CHOICE {
    create    NULL,
    join      NULL,
    invite    NULL,
    ...
  },
  callServices              QseriesOptions OPTIONAL,
  callType                  CallType,
  sourceCallSignalAddress   TransportAddress OPTIONAL,
  remoteExtensionAddress    AliasAddress OPTIONAL,
  callIdentifier            CallIdentifier,
  h245SecurityCapability    SEQUENCE OF H245Security OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                 SEQUENCE OF OCTET STRING OPTIONAL,
  mediaWaitForConnect       BOOLEAN,
  canOverlapSend            BOOLEAN,
  endpointIdentifier        EndpointIdentifier OPTIONAL,
  multipleCalls             BOOLEAN,
  maintainConnection        BOOLEAN,
  connectionParameters      SEQUENCE {
    connectionType          ScnConnectionType,
    numberOfScnConnections  INTEGER(0..65535),
    connectionAggregation   ScnConnectionAggregation
  } OPTIONAL,
  language                  SEQUENCE OF IA5String(SIZE(1..32)) OPTIONAL,
  presentationIndicator     PresentationIndicator OPTIONAL,
  screeningIndicator        ScreeningIndicator OPTIONAL,
  serviceControl            SEQUENCE OF ServiceControlSession OPTIONAL,
  symmetricOperationRequired NULL OPTIONAL,
  capacity                  CallCapacity OPTIONAL,
  circuitInfo               CircuitInfo OPTIONAL,
  desiredProtocols          SEQUENCE OF SupportedProtocols OPTIONAL,
  neededFeatures            SEQUENCE OF FeatureDescriptor OPTIONAL,
  desiredFeatures           SEQUENCE OF FeatureDescriptor OPTIONAL,
  supportedFeatures         SEQUENCE OF FeatureDescriptor OPTIONAL,
  parallelH245Control       SEQUENCE OF ParallelH245Control OPTIONAL,
  additionalSourceAddresses SEQUENCE OF ExtendedAliasAddress OPTIONAL,
  ...
}

-- =======================================================================
-- Other Message Types (simplified)
-- =======================================================================
CallProceeding-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  destinationInfo         EndpointType,
  h245Address             TransportAddress OPTIONAL,
  callIdentifier          CallIdentifier,
  h245SecurityMode        H245Security OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls           BOOLEAN,
  maintainConnection      BOOLEAN,
  fastConnectRefused      NULL OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  ...
}

Connect-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  h245Address             TransportAddress OPTIONAL,
  destinationInfo         EndpointType,
  conferenceID            ConferenceIdentifier,
  callIdentifier          CallIdentifier,
  h245SecurityMode        H245Security OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls           BOOLEAN,
  maintainConnection      BOOLEAN,
  language                SEQUENCE OF IA5String(SIZE(1..32)) OPTIONAL,
  connectedAddress        SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator   PresentationIndicator OPTIONAL,
  screeningIndicator      ScreeningIndicator OPTIONAL,
  fastConnectRefused      NULL OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL,
  capacity                CallCapacity OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  ...
}

Alerting-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  destinationInfo         EndpointType,
  h245Address             TransportAddress OPTIONAL,
  callIdentifier          CallIdentifier,
  h245SecurityMode        H245Security OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls           BOOLEAN,
  maintainConnection      BOOLEAN,
  alertingAddress         SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator   PresentationIndicator OPTIONAL,
  screeningIndicator      ScreeningIndicator OPTIONAL,
  fastConnectRefused      NULL OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL,
  capacity                CallCapacity OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  ...
}

Information-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  fastConnectRefused      NULL OPTIONAL,
  circuitInfo             CircuitInfo OPTIONAL,
  ...
}

ReleaseComplete-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  reason                  ReleaseCompleteReason OPTIONAL,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  busyAddress             SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator   PresentationIndicator OPTIONAL,
  screeningIndicator      ScreeningIndicator OPTIONAL,
  capacity                CallCapacity OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  ...
}

Facility-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  alternativeAddress      TransportAddress OPTIONAL,
  alternativeAliasAddress SEQUENCE OF AliasAddress OPTIONAL,
  conferenceID            ConferenceIdentifier OPTIONAL,
  reason                  FacilityReason,
  callIdentifier          CallIdentifier,
  destExtraCallInfo       SEQUENCE OF AliasAddress OPTIONAL,
  remoteExtensionAddress  AliasAddress OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  conferences             SEQUENCE OF ConferenceList OPTIONAL,
  h245Address             TransportAddress OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls           BOOLEAN,
  maintainConnection      BOOLEAN,
  fastConnectRefused      NULL OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL,
  circuitInfo             CircuitInfo OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  destinationInfo         EndpointType OPTIONAL,
  h245SecurityMode        H245Security OPTIONAL,
  ...
}

Progress-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  destinationInfo         EndpointType,
  h245Address             TransportAddress OPTIONAL,
  callIdentifier          CallIdentifier,
  h245SecurityMode        H245Security OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart               SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls           BOOLEAN,
  maintainConnection      BOOLEAN,
  fastConnectRefused      NULL OPTIONAL,
  ...
}

Status-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

StatusInquiry-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

SetupAcknowledge-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

Notify-UUIE ::= SEQUENCE {
  protocolIdentifier      ProtocolIdentifier,
  callIdentifier          CallIdentifier,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

-- =======================================================================
-- Supporting Types (simplified definitions)
-- =======================================================================
ProtocolIdentifier ::= OBJECT IDENTIFIER

EndpointType ::= SEQUENCE {
  nonStandardData         NonStandardParameter OPTIONAL,
  vendor                  VendorIdentifier OPTIONAL,
  gatekeeper              GatekeeperInfo OPTIONAL,
  gateway                 GatewayInfo OPTIONAL,
  mcu                     McuInfo OPTIONAL,
  terminal                TerminalInfo OPTIONAL,
  mc                      BOOLEAN,
  undefinedNode           BOOLEAN,
  ...
}

VendorIdentifier ::= SEQUENCE {
  vendor                  H221NonStandard,
  productId               OCTET STRING(SIZE(1..256)) OPTIONAL,
  versionId               OCTET STRING(SIZE(1..256)) OPTIONAL
}

H221NonStandard ::= SEQUENCE {
  t35CountryCode          INTEGER(0..255),
  t35Extension            INTEGER(0..255),
  manufacturerCode        INTEGER(0..65535)
}

GatekeeperInfo ::= SEQUENCE {...}
GatewayInfo ::= SEQUENCE {...}
McuInfo ::= SEQUENCE {...}
TerminalInfo ::= SEQUENCE {...}

QseriesOptions ::= SEQUENCE {...}
CallType ::= CHOICE {
  pointToPoint            NULL,
  oneToN                  NULL,
  nToOne                  NULL,
  nToN                    NULL,
  ...
}

H245Security ::= SEQUENCE {...}
ClearToken ::= SEQUENCE {...}
CryptoH323Token ::= SEQUENCE {...}
EndpointIdentifier ::= BMPString(SIZE(1..128))

ScnConnectionType ::= CHOICE {
  unknown                 NULL,
  bChannel                NULL,
  hybrid2x64              NULL,
  hybrid384               NULL,
  hybrid1536              NULL,
  hybrid1920              NULL,
  multirate               NULL,
  ...
}

ScnConnectionAggregation ::= CHOICE {
  auto                    NULL,
  none                    NULL,
  h221                    NULL,
  bonding-mode1           NULL,
  bonding-mode2           NULL,
  bonding-mode3           NULL,
  ...
}

PresentationIndicator ::= CHOICE {
  presentationAllowed     NULL,
  presentationRestricted  NULL,
  addressNotAvailable     NULL,
  ...
}

ScreeningIndicator ::= CHOICE {
  userProvidedNotScreened NULL,
  userProvidedVerifiedAndPassed NULL,
  userProvidedVerifiedAndFailed NULL,
  networkProvided         NULL,
  ...
}

ServiceControlSession ::= SEQUENCE {...}
CallCapacity ::= SEQUENCE {...}
CircuitInfo ::= SEQUENCE {...}
SupportedProtocols ::= SEQUENCE {...}
FeatureDescriptor ::= SEQUENCE {...}
ParallelH245Control ::= SEQUENCE {...}
ExtendedAliasAddress ::= SEQUENCE {...}
FeatureSet ::= SEQUENCE {...}

ReleaseCompleteReason ::= CHOICE {
  noBandwidth             NULL,
  gatekeeperResources     NULL,
  unreachableDestination  NULL,
  destinationRejection    NULL,
  invalidRevision         NULL,
  noPermission            NULL,
  unreachableGatekeeper   NULL,
  gatewayResources        NULL,
  badFormatAddress        NULL,
  adaptiveBusy            NULL,
  inConf                  NULL,
  undefinedReason         NULL,
  facilityCallDeflection  NULL,
  securityDenied          NULL,
  calledPartyNotRegistered NULL,
  callerNotRegistered     NULL,
  newConnectionNeeded     NULL,
  nonStandardReason       NonStandardParameter,
  replaceWithConferenceInvite ConferenceIdentifier,
  genericDataReason       NULL,
  neededFeatureNotSupported NULL,
  tunnelledSignallingRejected NULL,
  invalidCID              NULL,
  securityError           SecurityErrors2,
  hopCountExceeded        NULL,
  ...
}

FacilityReason ::= CHOICE {
  routeCallToGatekeeper   NULL,
  callForwarded           NULL,
  routeCallToMC           NULL,
  undefinedReason         NULL,
  conferenceListChoice    NULL,
  startH245               NULL,
  noH245                  NULL,
  newTokens               NULL,
  featureSetUpdate        NULL,
  forwardedElements       NULL,
  transportedInformation  NULL,
  ...
}

ConferenceList ::= SEQUENCE {...}
SecurityErrors2 ::= CHOICE {...}

END
