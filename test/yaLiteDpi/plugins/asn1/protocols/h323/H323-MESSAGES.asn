-- H323-MESSAGES.asn
--
-- H.323 Packet-based multimedia communications systems
-- Based on ITU-T Recommendation H.323
--

-- Module H323-MESSAGES (H.323:12/2009)
H323-MESSAGES {itu-t(0) recommendation(0) h(8) h323(323) version(0) 7 h323-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- Export all symbols
-- =======================================================================
-- Top level H.323 Messages
-- =======================================================================
H323Message ::= CHOICE {
  gatekeeperRequest        GatekeeperRequest,
  gatekeeperConfirm        GatekeeperConfirm,
  gatekeeperReject         GatekeeperReject,
  registrationRequest      RegistrationRequest,
  registrationConfirm      RegistrationConfirm,
  registrationReject       RegistrationReject,
  unregistrationRequest    UnregistrationRequest,
  unregistrationConfirm    UnregistrationConfirm,
  unregistrationReject     UnregistrationReject,
  admissionRequest         AdmissionRequest,
  admissionConfirm         AdmissionConfirm,
  admissionReject          AdmissionReject,
  bandwidthRequest         BandwidthRequest,
  bandwidthConfirm         BandwidthConfirm,
  bandwidthReject          BandwidthReject,
  disengageRequest         DisengageRequest,
  disengageConfirm         DisengageConfirm,
  disengageReject          DisengageReject,
  locationRequest          LocationRequest,
  locationConfirm          LocationConfirm,
  locationReject           LocationReject,
  infoRequest              InfoRequest,
  infoRequestResponse      InfoRequestResponse,
  nonStandardMessage       NonStandardMessage,
  unknownMessageResponse   UnknownMessageResponse,
  requestInProgress        RequestInProgress,
  resourcesAvailableIndicate ResourcesAvailableIndicate,
  resourcesAvailableConfirm ResourcesAvailableConfirm,
  infoRequestAck           InfoRequestAck,
  infoRequestNak           InfoRequestNak,
  serviceControlIndication ServiceControlIndication,
  serviceControlResponse   ServiceControlResponse,
  admissionConfirmSequence AdmissionConfirmSequence,
  ...
}

-- =======================================================================
-- Basic Types
-- =======================================================================
RequestSeqNum ::= INTEGER(1..65535)

TimeStamp ::= INTEGER(1..4294967295)

CallIdentifier ::= SEQUENCE {
  guid                     OCTET STRING(SIZE(16))
}

ConferenceIdentifier ::= OCTET STRING(SIZE(16))

EndpointIdentifier ::= BMPString(SIZE(1..128))

GatekeeperIdentifier ::= BMPString(SIZE(1..128))

BandWidth ::= INTEGER(0..4294967295)

CallReferenceValue ::= INTEGER(0..65535)

-- =======================================================================
-- Address Types
-- =======================================================================
TransportAddress ::= CHOICE {
  ipAddress                SEQUENCE {
    ip                     OCTET STRING(SIZE(4)),
    port                   INTEGER(0..65535)
  },
  ipSourceRoute            SEQUENCE {
    ip                     OCTET STRING(SIZE(4)),
    port                   INTEGER(0..65535),
    route                  SEQUENCE OF OCTET STRING(SIZE(4)),
    routing                CHOICE {
      strict               NULL,
      loose                NULL
    }
  },
  ipxAddress               SEQUENCE {
    node                   OCTET STRING(SIZE(6)),
    netnum                 OCTET STRING(SIZE(4)),
    port                   OCTET STRING(SIZE(2))
  },
  ip6Address               SEQUENCE {
    ip                     OCTET STRING(SIZE(16)),
    port                   INTEGER(0..65535)
  },
  netBios                  OCTET STRING(SIZE(16)),
  nsap                     OCTET STRING(SIZE(1..20)),
  nonStandardAddress       NonStandardParameter,
  ...
}

AliasAddress ::= CHOICE {
  dialedDigits             IA5String(SIZE(1..128)),
  h323-ID                  BMPString(SIZE(1..256)),
  url-ID                   IA5String(SIZE(1..512)),
  transportID              TransportAddress,
  email-ID                 IA5String(SIZE(1..512)),
  partyNumber              PartyNumber,
  mobileUIM                CHOICE {
    ansi-41-uim            OCTET STRING(SIZE(1..14)),
    gsm-uim                SEQUENCE {
      imsi                 OCTET STRING(SIZE(3..8)),
      tmsi                 OCTET STRING(SIZE(1..4)) OPTIONAL
    }
  },
  ...
}

PartyNumber ::= CHOICE {
  e164Number               PublicPartyNumber,
  dataPartyNumber          NumberDigits,
  telexPartyNumber         NumberDigits,
  privateNumber            PrivatePartyNumber,
  nationalStandardPartyNumber NumberDigits,
  ...
}

PublicPartyNumber ::= SEQUENCE {
  publicTypeOfNumber       PublicTypeOfNumber,
  publicNumberDigits       NumberDigits
}

PrivatePartyNumber ::= SEQUENCE {
  privateTypeOfNumber      PrivateTypeOfNumber,
  privateNumberDigits      NumberDigits
}

NumberDigits ::= IA5String(SIZE(1..128))

PublicTypeOfNumber ::= CHOICE {
  unknown                  NULL,
  internationalNumber      NULL,
  nationalNumber           NULL,
  networkSpecificNumber    NULL,
  subscriberNumber         NULL,
  abbreviatedNumber        NULL,
  ...
}

PrivateTypeOfNumber ::= CHOICE {
  unknown                  NULL,
  level2RegionalNumber     NULL,
  level1RegionalNumber     NULL,
  pISNSpecificNumber       NULL,
  localNumber              NULL,
  abbreviatedNumber        NULL,
  ...
}

-- =======================================================================
-- Endpoint Types
-- =======================================================================
EndpointType ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  vendor                   VendorIdentifier OPTIONAL,
  gatekeeper               GatekeeperInfo OPTIONAL,
  gateway                  GatewayInfo OPTIONAL,
  mcu                      McuInfo OPTIONAL,
  terminal                 TerminalInfo OPTIONAL,
  mc                       BOOLEAN,
  undefinedNode            BOOLEAN,
  set                      OCTET STRING(SIZE(4)) OPTIONAL,
  supportedTunnelledProtocols SEQUENCE OF TunnelledProtocol OPTIONAL,
  ...
}

VendorIdentifier ::= SEQUENCE {
  vendor                   H221NonStandard,
  productId                OCTET STRING(SIZE(1..256)) OPTIONAL,
  versionId                OCTET STRING(SIZE(1..256)) OPTIONAL
}

H221NonStandard ::= SEQUENCE {
  t35CountryCode           INTEGER(0..255),
  t35Extension             INTEGER(0..255),
  manufacturerCode         INTEGER(0..65535)
}

GatekeeperInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

GatewayInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  protocol                 SEQUENCE OF SupportedProtocols OPTIONAL,
  ...
}

McuInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  protocol                 SEQUENCE OF SupportedProtocols OPTIONAL,
  ...
}

TerminalInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

SupportedProtocols ::= CHOICE {
  nonStandardData          NonStandardParameter,
  h310                     H310Caps,
  h320                     H320Caps,
  h321                     H321Caps,
  h322                     H322Caps,
  h323                     H323Caps,
  h324                     H324Caps,
  voice                    VoiceCaps,
  t120-only                T120OnlyCaps,
  nonStandardProtocol      NonStandardProtocol,
  t38FaxAnnexbOnly         T38FaxAnnexbOnlyCaps,
  sip                      SIPCaps,
  ...
}

TunnelledProtocol ::= SEQUENCE {
  tunnelledProtocolID      TunnelledProtocolID,
  subIdentifier            IA5String(SIZE(1..64)) OPTIONAL
}

TunnelledProtocolID ::= CHOICE {
  tunnelledProtocolObjectID OBJECT IDENTIFIER,
  tunnelledProtocolAlternateID TunnelledProtocolAlternateIdentifier
}

TunnelledProtocolAlternateIdentifier ::= SEQUENCE {
  protocolType             IA5String(SIZE(1..64)),
  protocolVariant          IA5String(SIZE(1..64)) OPTIONAL
}

-- =======================================================================
-- Gatekeeper Discovery Messages
-- =======================================================================
GatekeeperRequest ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  rasAddress               TransportAddress,
  endpointType             EndpointType,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  callServices             QseriesOptions OPTIONAL,
  endpointAlias            SEQUENCE OF AliasAddress OPTIONAL,
  alternateEndpoints       SEQUENCE OF Endpoint OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  authenticationCapability SEQUENCE OF AuthenticationMechanism OPTIONAL,
  algorithmOIDs            SEQUENCE OF OBJECT IDENTIFIER OPTIONAL,
  integrity                SEQUENCE OF IntegrityMechanism OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  supportsAltGK            NULL OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

GatekeeperConfirm ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  rasAddress               TransportAddress,
  alternateGatekeeper      SEQUENCE OF AlternateGK OPTIONAL,
  authenticationMode       AuthenticationMechanism OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  algorithmOID             OBJECT IDENTIFIER OPTIONAL,
  integrity                SEQUENCE OF IntegrityMechanism OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

GatekeeperReject ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  rejectReason             GatekeeperRejectReason,
  altGKInfo                AltGKInfo OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

-- =======================================================================
-- Registration Messages
-- =======================================================================
RegistrationRequest ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  discoveryComplete        BOOLEAN,
  callSignalAddress        SEQUENCE OF TransportAddress,
  rasAddress               SEQUENCE OF TransportAddress,
  endpointType             EndpointType,
  endpointIdentifier       EndpointIdentifier OPTIONAL,
  alternateEndpoints       SEQUENCE OF Endpoint OPTIONAL,
  timeToLive               TimeToLive OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  keepAlive                BOOLEAN,
  endpointAlias            SEQUENCE OF AliasAddress OPTIONAL,
  willSupplyUUIEs          BOOLEAN,
  maintainConnection       BOOLEAN,
  alternateTransportAddresses AlternateTransportAddresses OPTIONAL,
  additiveRegistration     NULL OPTIONAL,
  terminalAlias            SEQUENCE OF AliasAddress OPTIONAL,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  endpointVendor           VendorIdentifier OPTIONAL,
  restart                  NULL OPTIONAL,
  supportsACFSequences     NULL OPTIONAL,
  applicationLayerProtocol SEQUENCE OF ApplicationLayerProtocol OPTIONAL,
  endpointCallSignallingAddress SEQUENCE OF TransportAddress OPTIONAL,
  capacity                 CallCapacity OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

RegistrationConfirm ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  callSignalAddress        SEQUENCE OF TransportAddress,
  terminalAlias            SEQUENCE OF AliasAddress OPTIONAL,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  endpointIdentifier       EndpointIdentifier,
  alternateGatekeeper      SEQUENCE OF AlternateGK OPTIONAL,
  timeToLive               TimeToLive OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  willRespondToIRR         BOOLEAN,
  preGrantedARQ            SEQUENCE OF PreGrantedARQ OPTIONAL,
  maintainConnection       BOOLEAN,
  serviceControl           SEQUENCE OF ServiceControlSession OPTIONAL,
  supportsAdditiveRegistration NULL OPTIONAL,
  terminalAliasPattern     SEQUENCE OF AddressPattern OPTIONAL,
  supportedPrefixes        SEQUENCE OF SupportedPrefix OPTIONAL,
  usageSpec                SEQUENCE OF RasUsageSpecification OPTIONAL,
  featureServerAlias       AliasAddress OPTIONAL,
  capacityReportingSpec    CapacityReportingSpecification OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

RegistrationReject ::= SEQUENCE {
  requestSeqNum            RequestSeqNum,
  protocolIdentifier       ProtocolIdentifier,
  nonStandardData          NonStandardParameter OPTIONAL,
  rejectReason             RegistrationRejectReason,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  altGKInfo                AltGKInfo OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  featureSet               FeatureSet OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  ...
}

-- =======================================================================
-- Supporting Types (simplified definitions)
-- =======================================================================
ProtocolIdentifier ::= OBJECT IDENTIFIER

NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier    NonStandardIdentifier,
  data                     OCTET STRING
}

NonStandardIdentifier ::= CHOICE {
  object                   OBJECT IDENTIFIER,
  h221NonStandard          H221NonStandard
}

QseriesOptions ::= SEQUENCE {...}
Endpoint ::= SEQUENCE {...}
ClearToken ::= SEQUENCE {...}
CryptoH323Token ::= SEQUENCE {...}
AuthenticationMechanism ::= SEQUENCE {...}
IntegrityMechanism ::= SEQUENCE {...}
ICV ::= SEQUENCE {...}
FeatureSet ::= SEQUENCE {...}
GenericData ::= SEQUENCE {...}
AlternateGK ::= SEQUENCE {...}
AltGKInfo ::= SEQUENCE {...}
TimeToLive ::= INTEGER(1..4294967295)
AlternateTransportAddresses ::= SEQUENCE {...}
ApplicationLayerProtocol ::= SEQUENCE {...}
CallCapacity ::= SEQUENCE {...}
PreGrantedARQ ::= SEQUENCE {...}
ServiceControlSession ::= SEQUENCE {...}
AddressPattern ::= SEQUENCE {...}
SupportedPrefix ::= SEQUENCE {...}
RasUsageSpecification ::= SEQUENCE {...}
CapacityReportingSpecification ::= SEQUENCE {...}

-- Protocol Capability Types
H310Caps ::= SEQUENCE {...}
H320Caps ::= SEQUENCE {...}
H321Caps ::= SEQUENCE {...}
H322Caps ::= SEQUENCE {...}
H323Caps ::= SEQUENCE {...}
H324Caps ::= SEQUENCE {...}
VoiceCaps ::= SEQUENCE {...}
T120OnlyCaps ::= SEQUENCE {...}
NonStandardProtocol ::= SEQUENCE {...}
T38FaxAnnexbOnlyCaps ::= SEQUENCE {...}
SIPCaps ::= SEQUENCE {...}

-- Reason Types
GatekeeperRejectReason ::= CHOICE {
  resourceUnavailable      NULL,
  terminalExcluded         NULL,
  invalidRevision          NULL,
  undefinedReason          NULL,
  securityDenial           NULL,
  genericDataReason        NULL,
  neededFeatureNotSupported NULL,
  securityError            SecurityErrors2,
  ...
}

RegistrationRejectReason ::= CHOICE {
  discoveryRequired        NULL,
  invalidRevision          NULL,
  invalidCallSignalAddress NULL,
  invalidRASAddress        NULL,
  duplicateAlias           SEQUENCE OF AliasAddress,
  invalidTerminalType      NULL,
  undefinedReason          NULL,
  transportNotSupported    NULL,
  transportQOSNotSupported NULL,
  resourceUnavailable      NULL,
  invalidAlias             NULL,
  securityDenial           NULL,
  qosControlNotSupported   NULL,
  incompleteAddress        NULL,
  aliasesInconsistent      NULL,
  routeCallToSCN           SEQUENCE OF PartyNumber,
  exceedsCallCapacity      NULL,
  collectiveAlias          NULL,
  securityError            SecurityErrors2,
  encodingError            NULL,
  transportedInformation   NULL,
  genericDataReason        NULL,
  neededFeatureNotSupported NULL,
  securityIntegrityFailed  NULL,
  ...
}

SecurityErrors2 ::= CHOICE {...}

-- Additional message types would be defined here for:
-- UnregistrationRequest, UnregistrationConfirm, UnregistrationReject
-- AdmissionRequest, AdmissionConfirm, AdmissionReject
-- BandwidthRequest, BandwidthConfirm, BandwidthReject
-- DisengageRequest, DisengageConfirm, DisengageReject
-- LocationRequest, LocationConfirm, LocationReject
-- InfoRequest, InfoRequestResponse
-- And other H.323 RAS messages...

END
