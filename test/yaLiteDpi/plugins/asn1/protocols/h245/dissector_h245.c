#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#include "asn1_discovery.h"

#define PROTO_NAME     "h245"
#define HEADER_LEN_H245 12

static
int h245_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode SNMP message using asn1c
    MultimediaSystemControlMessage_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_MultimediaSystemControlMessage, (void **)&message, data, data_len);
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_MultimediaSystemControlMessage, message);
    return HEADER_LEN_H245;
}

static
int h245_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "h245");
     pschema_register_field(pschema, "flag",      YA_FT_UINT8,  "h245 flag");
     pschema_register_field(pschema, "p_type",    YA_FT_UINT8,  "h245 payload type");
     pschema_register_field(pschema, "seq",       YA_FT_UINT16, "h245 seq");
     pschema_register_field(pschema, "timestamp", YA_FT_UINT32, "h245 timestamp");
     pschema_register_field(pschema, "ssrc",      YA_FT_BYTES,  "h245 ssrc");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "h245",
    .schemaRegFun = h245_schema_reg,
    .dissectFun   = h245_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 8000),
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h245)
{
    nxt_dissector_register(&gDissectorDef);
}
