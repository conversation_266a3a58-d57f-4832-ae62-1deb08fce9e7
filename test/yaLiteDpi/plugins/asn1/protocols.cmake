# Protocol configuration file
# This file defines all protocols that need ASN.1 parsing support
# To add a new protocol, simply add it to the ASN1_PROTOCOLS list

# List of protocols that require ASN.1 parsing
set(ASN1_PROTOCOLS
    snmp
    h245
    h225
    h235
    h323
    # Add new protocols here, one per line
    # Example:
    # ldap
    # x509
    # kerberos
)

# Function to setup all ASN.1 protocols
function(setup_asn1_protocols)
    # Include the ASN.1 function definitions
    message("Function to setup all ASN.1 protocols")
    include(${CMAKE_CURRENT_SOURCE_DIR}/asn1/asn1_functions.cmake)

    # Generate parsers for all configured protocols
    foreach(PROTOCOL ${ASN1_PROTOCOLS})
        message(STATUS "Setting up ASN.1 parser for protocol: ${PROTOCOL}")
        generate_asn1_parser(${PROTOCOL})
        generate_asn1_discovery_header(${PROTOCOL})

        # Check if the target was created successfully
        if(TARGET asn1_${PROTOCOL})
            message(STATUS "ASN.1 target asn1_${PROTOCOL} created successfully")
            # Export the target to parent scope
            set(ASN1_${PROTOCOL}_AVAILABLE TRUE PARENT_SCOPE)
        else()
            message(WARNING "Failed to create ASN.1 target for ${PROTOCOL}")
            set(ASN1_${PROTOCOL}_AVAILABLE FALSE PARENT_SCOPE)
        endif()
    endforeach()
endfunction()

# Function to check if a protocol has ASN.1 support
function(has_asn1_support PROTOCOL_NAME RESULT_VAR)
    list(FIND ASN1_PROTOCOLS ${PROTOCOL_NAME} PROTOCOL_INDEX)
    if(PROTOCOL_INDEX GREATER_EQUAL 0)
        set(${RESULT_VAR} TRUE PARENT_SCOPE)
    else()
        set(${RESULT_VAR} FALSE PARENT_SCOPE)
    endif()
endfunction()

# Function to get ASN.1 library name for a protocol
function(get_asn1_library PROTOCOL_NAME LIBRARY_VAR)
    has_asn1_support(${PROTOCOL_NAME} HAS_SUPPORT)
    if(HAS_SUPPORT AND TARGET asn1_${PROTOCOL_NAME})
        set(${LIBRARY_VAR} asn1_${PROTOCOL_NAME} PARENT_SCOPE)
    else()
        set(${LIBRARY_VAR} "" PARENT_SCOPE)
    endif()
endfunction()

# Function to automatically configure plugins for all ASN.1 protocols
function(configure_asn1_plugins)
    foreach(PROTOCOL ${ASN1_PROTOCOLS})
        # Check if dissector source file exists
        set(DISSECTOR_SOURCE "${CMAKE_CURRENT_SOURCE_DIR}/asn1/protocols/${PROTOCOL}/dissector_${PROTOCOL}.c")

        if(EXISTS ${DISSECTOR_SOURCE})
            message(STATUS "Configuring plugin for protocol: ${PROTOCOL}")

            # Check if protocol has ASN.1 support
            has_asn1_support(${PROTOCOL} HAS_SUPPORT)

            if(HAS_SUPPORT)
                # Configure plugin with ASN.1 support
                addEngineNextPlugin(${PROTOCOL} ${CMAKE_SOURCE_DIR}/bin/plugins
                  SOURCES ${DISSECTOR_SOURCE}
                  LINK_LIBRARIES "-Wl,--whole-archive" asn1_${PROTOCOL} "-Wl,--no-whole-archive"
                )
                # Add dependency manually after plugin creation
                add_dependencies(yaNxtDissector_${PROTOCOL} asn1_build_${PROTOCOL})
                message(STATUS "${PROTOCOL} plugin configured with ASN.1 support")
            else()
                # Configure plugin without ASN.1 support
                message(WARNING "${PROTOCOL} plugin will be built without ASN.1 support")
                addEngineNextPlugin(${PROTOCOL} ${CMAKE_SOURCE_DIR}/bin/plugins
                  SOURCES ${DISSECTOR_SOURCE}
                )
            endif()
        else()
            message(STATUS "Dissector source file not found for ${PROTOCOL}: ${DISSECTOR_SOURCE}")
            message(STATUS "Skipping plugin configuration for ${PROTOCOL}")
        endif()
    endforeach()
endfunction()

# Function to automatically link all ASN.1 libraries to unit tests
function(link_asn1_libraries_to_target TARGET_NAME)
    set(LINKED_COUNT 0)
    set(LINKED_PROTOCOLS "")

    foreach(PROTOCOL ${ASN1_PROTOCOLS})
        # Check if the protocol has ASN.1 support configured
        has_asn1_support(${PROTOCOL} HAS_SUPPORT)
        if(HAS_SUPPORT)
            # For IMPORTED libraries, we need to link them and add build dependencies
            target_link_libraries(${TARGET_NAME} PUBLIC asn1_${PROTOCOL})
            # Add dependency on the build target to ensure library is built first
            add_dependencies(${TARGET_NAME} asn1_build_${PROTOCOL})
            math(EXPR LINKED_COUNT "${LINKED_COUNT} + 1")
            list(APPEND LINKED_PROTOCOLS ${PROTOCOL})
            message(STATUS "Linked ASN.1 library asn1_${PROTOCOL} to ${TARGET_NAME}")
        else()
            message(STATUS "ASN.1 library asn1_${PROTOCOL} not available for ${TARGET_NAME}")
        endif()
    endforeach()

    if(LINKED_COUNT GREATER 0)
        string(REPLACE ";" ", " PROTOCOLS_STR "${LINKED_PROTOCOLS}")
        message(STATUS "${TARGET_NAME} linked with ${LINKED_COUNT} ASN.1 libraries: ${PROTOCOLS_STR}")
    else()
        message(WARNING "${TARGET_NAME} will be built without any ASN.1 library support")
    endif()
endfunction()