# ASN.1 parser generation functions
# This file contains the core functions for generating ASN.1 parsers

# Helper function to collect all C files from a directory
function(collect_c_files_from_directory DIRECTORY_PATH OUTPUT_VAR)
    if(EXISTS ${DIRECTORY_PATH})
        file(GLOB C_FILES "${DIRECTORY_PATH}/*.c")
        set(${OUTPUT_VAR} ${C_FILES} PARENT_SCOPE)
    else()
        set(${OUTPUT_VAR} "" PARENT_SCOPE)
    endif()
endfunction()

# Function to generate ASN.1 parser for a specific protocol
function(generate_asn1_parser PROTOCOL_NAME)
    # Use absolute paths to work from any calling directory
    set(ASN1_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/asn1")
    message(STATUS "ASN.1 source dir: ${ASN1_SOURCE_DIR}")
    set(PROTOCOL_DIR "${ASN1_SOURCE_DIR}/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_BINARY_DIR}/yaLiteDpi/plugins/asn1/generated/${PROTOCOL_NAME}")

    # Find ASN.1 files for this protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(WARNING "No ASN.1 files found for protocol ${PROTOCOL_NAME} in ${PROTOCOL_DIR}")
        return()
    endif()

    message(STATUS "Found ASN.1 files for ${PROTOCOL_NAME}: ${ASN1_FILES}")

    # Create output directory
    file(MAKE_DIRECTORY ${GENERATED_DIR})

    # Discovery header and script paths
    set(DISCOVERY_HEADER "${GENERATED_DIR}/asn1_discovery.h")
    set(DISCOVERY_SCRIPT "${CMAKE_CURRENT_SOURCE_DIR}/asn1/asn1_inspector.py")

    # Create a marker file to indicate when ASN.1 generation is complete
    set(GENERATION_MARKER "${GENERATED_DIR}/.asn1_generated")

    # Custom command to generate ASN.1 files
    add_custom_command(
        OUTPUT ${GENERATION_MARKER}
        COMMAND ${CMAKE_COMMAND} -E remove_directory ${GENERATED_DIR}
        COMMAND ${CMAKE_COMMAND} -E make_directory ${GENERATED_DIR}
        COMMAND python3 ${DISCOVERY_SCRIPT} ${PROTOCOL_NAME} ${DISCOVERY_HEADER} ${ASN1_FILES}
        COMMAND asn1c -fcompound-names -D ${GENERATED_DIR} ${ASN1_FILES}
        COMMAND ${CMAKE_COMMAND} -E touch ${GENERATION_MARKER}
        DEPENDS ${ASN1_FILES} ${DISCOVERY_SCRIPT}
        WORKING_DIRECTORY ${ASN1_SOURCE_DIR}
        COMMENT "Generating ASN.1 parser for ${PROTOCOL_NAME}"
    )

    # Create a custom target for the generation step
    set(GENERATION_TARGET "asn1_generate_${PROTOCOL_NAME}")
    add_custom_target(${GENERATION_TARGET} DEPENDS ${GENERATION_MARKER})

    # Simple and reliable solution: Use a wildcard pattern that CMake will resolve at build time
    # This avoids the "chicken and egg" problem by not requiring files to exist during configuration

    # Create a CMake script that will be executed at build time to create the actual library
    set(BUILD_LIBRARY_SCRIPT "${CMAKE_BINARY_DIR}/build_asn1_library_${PROTOCOL_NAME}.cmake")

    file(WRITE ${BUILD_LIBRARY_SCRIPT} "
# Build script for ASN.1 library ${PROTOCOL_NAME}

# Find all generated C files
file(GLOB ASN1_SOURCES \"${GENERATED_DIR}/*.c\")

if(NOT ASN1_SOURCES)
    message(FATAL_ERROR \"No ASN.1 generated files found in ${GENERATED_DIR}\")
endif()

list(LENGTH ASN1_SOURCES SOURCE_COUNT)
message(STATUS \"Building ASN.1 library ${PROTOCOL_NAME} with \${SOURCE_COUNT} source files\")

# The actual compilation will be handled by the custom target below
")

    # Create a custom target that:
    # 1. Generates ASN.1 files
    # 2. Compiles them into object files
    # 3. Creates a static library
    set(BUILD_TARGET_NAME "asn1_build_${PROTOCOL_NAME}")
    add_custom_target(${BUILD_TARGET_NAME}
        # First, ensure ASN.1 files are generated
        COMMAND ${CMAKE_COMMAND} --build . --target ${GENERATION_TARGET}

        # Then, compile all generated C files into object files
        COMMAND ${CMAKE_COMMAND} -E echo "Compiling ASN.1 sources for ${PROTOCOL_NAME}..."
        COMMAND ${CMAKE_COMMAND} -P ${BUILD_LIBRARY_SCRIPT}

        # Create the library using ar (archiver)
        COMMAND ${CMAKE_COMMAND} -E echo "Creating ASN.1 library for ${PROTOCOL_NAME}..."
        COMMAND bash -c "cd ${GENERATED_DIR} && rm -f converter-example.c pdu_collection.c && ${CMAKE_C_COMPILER} -c -fPIC -I. -Wno-unused-parameter -Wno-unused-variable -Wno-unused-function -Wno-sign-compare -Wno-missing-field-initializers -Wno-type-limits -Wno-error *.c && ar rcs ${CMAKE_BINARY_DIR}/libasn1_${PROTOCOL_NAME}.a *.o && echo 'Library created: ${CMAKE_BINARY_DIR}/libasn1_${PROTOCOL_NAME}.a'"

        COMMENT "Building ASN.1 library for ${PROTOCOL_NAME}"
        VERBATIM
    )

    # Create an INTERFACE library that provides the include directories and library path
    add_library(asn1_${PROTOCOL_NAME} INTERFACE)

    # Set the include directories
    target_include_directories(asn1_${PROTOCOL_NAME} INTERFACE ${GENERATED_DIR})

    # Set the library file as a link library using the full path
    set(LIBRARY_PATH "${CMAKE_BINARY_DIR}/libasn1_${PROTOCOL_NAME}.a")
    target_link_libraries(asn1_${PROTOCOL_NAME} INTERFACE ${LIBRARY_PATH})

    # Make sure the interface library depends on our custom target
    add_dependencies(asn1_${PROTOCOL_NAME} ${BUILD_TARGET_NAME})

    # Export the target name for parent scope
    set(ASN1_${PROTOCOL_NAME}_TARGET asn1_${PROTOCOL_NAME} PARENT_SCOPE)
endfunction()

# Function to generate ASN.1 structure discovery header
function(generate_asn1_discovery_header PROTOCOL_NAME)
    set(PROTOCOL_DIR "${CMAKE_CURRENT_SOURCE_DIR}/asn1/protocols/${PROTOCOL_NAME}")
    set(GENERATED_DIR "${CMAKE_CURRENT_BINARY_DIR}/asn1/generated/${PROTOCOL_NAME}")
    set(DISCOVERY_HEADER "${GENERATED_DIR}/asn1_discovery.h")

    # Find all ASN.1 files for the protocol
    file(GLOB ASN1_FILES "${PROTOCOL_DIR}/*.asn")

    if(NOT ASN1_FILES)
        message(FATAL_ERROR "No ASN.1 files found in ${PROTOCOL_DIR}")
    endif()

    # Use the existing asn1_inspector.py script for discovery header generation
    set(DISCOVERY_SCRIPT "${CMAKE_CURRENT_SOURCE_DIR}/asn1/asn1_inspector.py")

    # Make script executable
    execute_process(COMMAND chmod +x ${DISCOVERY_SCRIPT})

    # Generate discovery header
    add_custom_command(
        OUTPUT ${DISCOVERY_HEADER}
        COMMAND python3 ${DISCOVERY_SCRIPT} ${PROTOCOL_NAME} ${DISCOVERY_HEADER} ${ASN1_FILES}
        DEPENDS ${ASN1_FILES} ${DISCOVERY_SCRIPT}
        COMMENT "Generating ASN.1 discovery header for ${PROTOCOL_NAME}"
    )

    # Add to generated files
    set(DISCOVERY_TARGET_NAME asn1_discovery_${PROTOCOL_NAME})
    add_custom_target(${DISCOVERY_TARGET_NAME} DEPENDS ${DISCOVERY_HEADER})

    # Export for parent scope
    set(ASN1_DISCOVERY_HEADER_${PROTOCOL_NAME} ${DISCOVERY_HEADER} PARENT_SCOPE)
    set(ASN1_DISCOVERY_TARGET_${PROTOCOL_NAME} ${DISCOVERY_TARGET_NAME} PARENT_SCOPE)
endfunction()
