#include <yaEngineNext/nxt_engine.h>
#include <pcap/pcap.h>
#include <glib-2.0/glib.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>

#include <string>
#include <errno.h>
#include <yaBasicUtils/filesystem.hpp>

#include <string.h>
#include <libgen.h> // for api basename

struct sessionContext
{
public:
    char sessionName[60];
    GChecksum *checksum = NULL;

public:
    sessionContext():sessionName{}, checksum(NULL)
    {
    }
};

int show_field_kv(pfield_t *field, const char *kvFormat)
{
    pfield_desc_t *desc  = precord_field_get_fdesc(field);
    ya_fvalue_t   *value = precord_field_get_fvalue(field);
    if (NULL == value)
    {
        return 0;
    }

    char *valueString = ya_fvalue_to_string_repr(value, BASE_NONE);
    printf(kvFormat, pfdesc_get_name(desc), valueString);
    ya_fvalue_free_string_repr(valueString);

    return 0;
}

int show_proto_record(precord_t *precord)
{
    player_t *layer = precord_layer_get_top(precord);
    const char *protoName = precord_layer_get_layer_name(layer);

    printf("proto %s {", protoName);

    // 打印第一个非空 field
    pfield_t *field = NULL;
    for (field = precord_field_get_first_from_layer_of(layer);
         field != NULL && precord_field_get_fvalue(field) == NULL;
         field = precord_field_get_next(precord, field))
        ; // blank
    show_field_kv(field, "%s: %s");

    // 打印剩余 field
    for (field = precord_field_get_next(precord, field);
         field != NULL;
         field = precord_field_get_next(precord, field))
    {
        show_field_kv(field, ", %s: %s");
    }

    printf("}\n");

    return 0;
}

int calc_story_checksum(precord_t *precord, void *userdata)
{
    player_t *layer = precord_layer_get_by_name(precord, "story");
    if (NULL == layer)
    {
        return -1;
    }

    ya_fvalue_t* valueStory = precord_fvalue_get_from_layer_of(layer, "story");
    if (NULL == valueStory)
    {
        return -1;
    }

    // printf("%s", ya_fvalue_get_string(value_story));

    GChecksum* checksum = (GChecksum *)(userdata);
    g_checksum_update(checksum, (guchar *)ya_fvalue_get(valueStory), ya_fvalue_length(valueStory));
    return 0;
}

typedef int (*cb_on_packet_func)(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp, void *userdata);

int pcap_packet_foreach(const char *filepath, cb_on_packet_func onPacket, void *userdata)
{
    // open pcap
    char errbuf[PCAP_ERRBUF_SIZE] = { 0 };
    pcap_t *pcapHandle = pcap_open_offline(filepath, errbuf);
    if (NULL == pcapHandle)
    {
        printf("pcap_open_offline error:%s\n", errbuf);
        return -1;
    }

    pcap_pkthdr   *pktHeader = NULL;
    const uint8_t *pktData   = NULL;
    int            lSts      = 0;

    for (; ; )
    {
        lSts = pcap_next_ex(pcapHandle, &pktHeader, &pktData);
        if (0 == lSts || 1 == lSts)
        {  // OK, copy it
            onPacket(pktData, pktHeader->len, pktHeader->ts.tv_sec, userdata);
        }
        else if (-1 == lSts)
        {   // error
            fprintf(stderr, "%s\n", pcap_geterr(pcapHandle));
            break;
        }
        else if (-2 == lSts)
        {   // no more pkt to read from offline file
            break;
        }
    }

    pcap_close(pcapHandle);
    return 0;
}

int event_handler_session_message(nxt_engine_t *engine, nxt_pmessage_t *message, void *userdata)
{
    auto           sessionCtx = (struct sessionContext *)(userdata);
    precord_t*     precord    = nxt_message_get_precord(message);
    nxt_session_t *session    = nxt_message_get_session(message);

    if (strcmp(sessionCtx->sessionName, "") == 0 && session)
    {
        const char *sessionRepr = nxt_session_get_str_repr(engine, session);
        strncpy(sessionCtx->sessionName, sessionRepr, sizeof sessionCtx->sessionName - 1); // 需要预留一个长度给 \0;
    }

    calc_story_checksum(precord, sessionCtx->checksum);

    return 0;
}

int nxt_cb_on_packet(const uint8_t *pktData, uint32_t pktLen, uint64_t timestamp _U_, void *userdata)
{
    nxt_engine_t *engine = (nxt_engine_t *)(userdata);
    nxt_mbuf_t *mbuf = nxt_mbuf_new_by_copy_wa(nxt_engine_get_allocator(engine), pktData, pktLen);

    // 输入 mbuf 到 engine 中，推动 engine 运转;
    // TODO: 需要将 init 与 free 放到 nxt_engine_run 中进行;
    nxt_engine_run(engine, mbuf);
    nxt_mbuf_free_wa(nxt_engine_get_allocator(engine), mbuf);

    return 0;
}

int load_plugins()
{
    // 加载插件
    char errorBuff[255] = { 0 };
    std::string pluginDir(yv::getAppDir() + "plugins");

    const char kPluginPrefix[] = "yaNxtDissector";

    yv::forDirEntry(pluginDir.c_str(), [&](const char *filePath, bool)
    {
        const char *fileName = basename(const_cast<char*>(filePath));
        if (strncmp(fileName, kPluginPrefix, sizeof(kPluginPrefix) - 1) != 0)
        {   // not a yaNxtDissector
            return -1;
        }

        nxt_plugin_dissector_load(filePath, errorBuff, sizeof errorBuff);
        printf("load plugin done: %s\n", filePath);
        return 0;
    });

    return 0;
}

int main(int, char *argv[])
{
    nxt_init();

    load_plugins();

    // 创建 engine
    nxt_engine_t *engine  = nxt_engine_create(nullptr);

    sessionContext sessionCtx;
    sessionCtx.checksum = g_checksum_new(G_CHECKSUM_MD5);

    // 添加事件处理器
    nxt_engine_add_event_handler(engine, NXT_EVENT_SESSION_MESSAGE, event_handler_session_message, &sessionCtx);

    // 解析 pcap 中的 packet
    pcap_packet_foreach(argv[1], nxt_cb_on_packet, engine);

    // 销毁 engine
    nxt_engine_destroy(engine);

    nxt_fini();

    // show checksum
    printf("stream [%s] md5:%s\n", sessionCtx.sessionName, g_checksum_get_string(sessionCtx.checksum));
    g_checksum_free(sessionCtx.checksum);
    return 0;
}
